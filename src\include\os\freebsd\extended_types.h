/* SPDX-License-Identifier: LGPL-3.0-or-later */
/*
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public License
 * as published by the Free Software Foundation; either version 3 of
 * the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 * ---------------------------------------
 */

/**
 * @file os/freebsd/extended_types.h
 * @brief Extended type, FreeBSD version.
 */

#ifndef _EXTENDED_TYPES_FREEBSD_H
#define _EXTENDED_TYPES_FREEBSD_H

#include <sys/types.h>

#endif /* _EXTENDED_TYPES_FREEBSD_H */
