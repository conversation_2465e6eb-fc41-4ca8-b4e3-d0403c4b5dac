This files list the people, outside of the original development team that help in the project.
Their contributions were very helpful, so I guess they should have their names listed somewhere.

Thanks to:
   Contributors that are not colleagues or students
	- <PERSON> "spot" Callaway <tcallaw<PERSON>@redhat.com>, for his experience and wiseness in RPM packaging.
	- People from mailing-list <EMAIL> (with special attention to <PERSON><PERSON>
	  and <PERSON><PERSON><PERSON>) for their help understanding the complex logic within NFSv4
	- <PERSON> <<EMAIL>> who provided a nice patch fixing memleaks.
	- <PERSON> <<EMAIL>> who did lots of work to make libnfsidmap work with gssrpc in nfs-ganesha
	- <PERSON> <<EMAIL>> who provides me with several typos and memleaks patches
	- <PERSON><PERSON><PERSON> <<EMAIL>> who implemented NLMv4 support (locking for NFSv3)
	- <PERSON> <<EMAIL>> who provided me with several small fixes on the server's behavior. He designed
          and wrote as well the new Log layer API.

   Students that came to make an internship with us and worked on the project
	- <PERSON><PERSON> who wrote the first release of the FSAL_POSIX
	- <PERSON><PERSON> who wrote a PERL library that had been very useful to quickly write benchmarks
          and non-regression test
	- <PERSON>dric Cabessa who worked on the SNMP admin module
	- Adrien Grellier who provided us with the first TI-RPC support in the product
        - Rémi Duraffort who wrote the FSAL_ZFS implementation as well as the libzfswrap contrib. He fixed many other small bugs too
