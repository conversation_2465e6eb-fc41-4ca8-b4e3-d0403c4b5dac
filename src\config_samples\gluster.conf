###################################################
#
# EXPORT
#
# To function, all that is required is an EXPORT
#
# Define the absolute minimal export
#
###################################################

EXPORT
{
	# Export Id (mandatory, each EXPORT must have a unique Export_Id)
	Export_Id = 77;

	# Exported path (mandatory)
	Path = "/testvol";

	# Pseudo Path (required for NFS v4)
	Pseudo = "/testvol";

	# Required for access (default is None)
	# Could use CLIENT blocks instead
	Access_Type = RW;

	# Allow root access
	Squash = No_Root_Squash;

	# Security flavor supported
	SecType = "sys";

	# Exporting FSAL
	FSAL {
		Name = "GLUSTER";
		Hostname = localhost;
		Volume = "testvol";
		enable_upcall = true;
		Transport = tcp; # tcp or rdma
	}
}
