# dirs
oldtars/
linuxbox-ceph/
src/Docs/html/
src/Docs/latex/
src/autom4te.cache/
src/build-aux/
src/rpm/
src/pkg-deb/
src/docker/
src/.settings/
.deps/
.libs/
CMakeFiles/
build/
.cache/
.vscode/

# files
*.kdevelop*
*.kdevses
TAGS
tags
Makefile.in
Makefile
libtool
configure
depcomp
missing
ltmain.sh
install-sh
config.*
aclocal.m4
*.o
*.a
libganeshaNFS.pc
nfs-ganesha.spec
cppcheck.*
cscope.*
ylwrap
nfs-ganesha*.tar.gz
nfs-ganesha*.tar.bz2
patch-for-HPSS-nfs-ganesha-*
*.diff
*.patch
!debian/patches/*.patch
core.*
*cmake_install.cmake
libfsal*.so*
libganesha_nfsd.so*
libgmonitoring.so*
libganesha_trace.so*
Doxyfile
*~
*.swp
.project
.checkpatch.conf
nohup.out
src/scripts/systemd/nfs-ganesha-config.service
src/FSAL/FSAL_VFS/vfs/lustre_main.c
src/FSAL/FSAL_VFS/vfs/main.c
src/FSAL/FSAL_VFS/vfs/dummy_lustre_main.c
src/Protocols/NLM/sm_notify.ganesha
src/sm_notify.ganesha
src/ganesha.nfsd
src/CMakeCache.txt
src/CPackConfig.cmake
src/CPackSourceConfig.cmake
src/.cproject
src/tools/multilock/ml_cephfs_client
src/CMakeDoxyfile.in
src/CMakeDoxygenDefaults.cmake
src/SAL/libganesha_rados_recov.so
src/config_parsing/libganesha_rados_urls.so
src/ganesha-rados-grace
src/install_manifest.txt
src/sm_notify.ganesha
src/tools/multilock/ml_cephfs_client
src/tools/multilock/ml_console
src/tools/multilock/ml_glusterfs_client
src/tools/multilock/ml_posix_client
.DS_Store
src/compile_commands.json
gsh_lttng_generation_rules.cmake
gsh_lttng_generation_file_properties.cmake
gsh_generated_include
