// SPDX-License-Identifier: unknown license...
/*
 * The content of this file is a mix of rpcgen-generated
 * and hand-edited program text.  It is not automatically
 * generated by, e.g., build processes.
 *
 * This file is under version control.
 */

#ifndef _NFSACL_H_RPCGEN
#define _NFSACL_H_RPCGEN

#include <rpc/rpc.h>

#define NFS_ACL 0x0001
#define NFS_ACLCNT 0x0002
#define NFS_DFACL 0x0004
#define NFS_DFACLCNT 0x0008

#define NFS_ACL_MASK 0x00FF
#define NFS_DFACL_MASK 0x1000

struct attr3 {
	bool_t attributes_follow;
	union {
		fattr3 obj_attributes;
	} attr3_u;
};
typedef struct attr3 attr3;

struct posix_acl_entry {
	nfs3_uint32 e_tag;
	nfs3_uint32 e_id;
	nfs3_uint32 e_perm;
};
typedef struct posix_acl_entry posix_acl_entry;

struct posix_acl {
	nfs3_uint32 count;
	posix_acl_entry entries[0];
};
typedef struct posix_acl posix_acl;

struct getaclargs {
	nfs_fh3 fhandle;
	nfs3_int32 mask;
};
typedef struct getaclargs getaclargs;

struct getaclresok {
	attr3 attr;
	nfs3_int32 mask;
	nfs3_uint32 acl_access_count;
	posix_acl *acl_access;
	nfs3_uint32 acl_default_count;
	posix_acl *acl_default;
};
typedef struct getaclresok getaclresok;

struct getaclres {
	nfsstat3 status;
	union {
		getaclresok resok;
	} getaclres_u;
};
typedef struct getaclres getaclres;

struct setaclargs {
	nfs_fh3 fhandle;
	nfs3_int32 mask;
	nfs3_uint32 acl_access_count;
	posix_acl *acl_access;
	nfs3_uint32 acl_default_count;
	posix_acl *acl_default;
};
typedef struct setaclargs setaclargs;

struct setaclresok {
	attr3 attr;
};
typedef struct setaclresok setaclresok;

struct setaclres {
	nfsstat3 status;
	union {
		setaclresok resok;
	} setaclres_u;
};
typedef struct setaclres setaclres;

#define NFSACLPROG 100227
#define NFSACL_V3 3

#define NFSACLPROC_NULL 0
#define NFSACLPROC_GETACL 1
#define NFSACLPROC_SETACL 2

/* the xdr functions */

extern bool_t xdr_attr3(XDR *, attr3 *);
extern bool_t xdr_posix_acl_entry(XDR *, posix_acl_entry *);
extern bool_t xdr_posix_acl(XDR *, posix_acl *);
extern bool_t xdr_getaclargs(XDR *, getaclargs *);
extern bool_t xdr_getaclresok(XDR *, getaclresok *);
extern bool_t xdr_getaclres(XDR *, getaclres *);
extern bool_t xdr_setaclargs(XDR *, setaclargs *);
extern bool_t xdr_setaclresok(XDR *, setaclresok *);
extern bool_t xdr_setaclres(XDR *, setaclres *);

#endif /* !_NFSACL_H_RPCGEN */
