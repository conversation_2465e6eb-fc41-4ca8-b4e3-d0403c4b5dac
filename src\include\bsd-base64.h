/* SPDX-License-Identifier: unknown 0BSD */
/*
 * Copyright (c) 1996 by Internet Software Consortium.
 *
 * Permission to use, copy, modify, and distribute this software for
 * any purpose with or without fee is hereby granted, provided that
 * the above copyright notice and this permission notice appear in all
 * copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND INTERNET SOFTWARE CONSORTIUM
 * D<PERSON><PERSON>AIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT
 * SHALL INTERNET SOFTWARE CONSORTIUM BE LIABLE FOR ANY SPECIAL,
 * DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN
 * AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING
 * OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS
 * SOFTWARE.
 */

/*
 * Portions Copyright (c) 1995 by International Business Machines, Inc.
 *
 * International Business Machines, Inc. (hereinafter called IBM)
 * grants permission under its copyrights to use, copy, modify, and
 * distribute this Software with or without fee, provided that the
 * above copyright notice and all paragraphs of this notice appear in
 * all copies, and that the name of IBM not be used in connection with
 * the marketing of any product incorporating the Software or
 * modifications thereof, without specific, written prior permission.
 *
 * To the extent it has a right to do so, IBM grants an immunity from
 * suit under its patents, if any, for the use, sale or manufacture of
 * products to the extent that such products are used for performing
 * Domain Name System dynamic updates in TCP/IP networks by means of
 * the Software.  No immunity is granted for any product per se or for
 * any other function of any product.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", AND IBM DISCLAIMS ALL WARRANTIES,
 * INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE.  IN NO EVENT SHALL IBM BE LIABLE FOR ANY
 * SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE, EVEN IF IBM IS APPRISED OF THE
 * POSSIBILITY OF SUCH DAMAGES.
 */

#ifndef _BSD_BASE64_H
#define _BSD_BASE64_H

#include <sys/types.h>

int b64_ntop(u_char const *src, size_t srclength, char *target,
	     size_t targsize);
int b64_pton(char const *src, u_char *target, size_t targsize);
int base64url_encode(u_char const *src, size_t srclength, char *target,
		     size_t targsize);
#define __b64_ntop b64_ntop
#define __b64_pton b64_pton

#endif /* _BSD_BINRESVPORT_H */
