{ global:
  ace_count;
  admin_halt;
  admin_shutdown;
  avltree_do_insert;
  avltree_init;
  avltree_next;
  avltree_remove;
  avltree_sup;
  base64url_encode;
  change_fsid_type;
  check_share_conflict;
  check_verifier_attrlist;
  check_verifier_stat;
  CityHash64;
  clear_op_context_export;
  close_fsal_fd;
  compound_data_Free;
  component_log_level;
  config_error_no_error;
  config_errs_to_log;
  config_Free;
  config_GetBlockNode;
  config_ParseFile;
  config_url_init;
  connection_manager__callback_set;
  connection_manager__callback_clear;
  connection_manager__drain_and_disconnect_local;
  convert_ipv6_to_ipv4;
  create_log_facility;
  decode_fsid;
  def_pnfs_ds_ops;
  default_mutex_attr;
  default_rwlock_attr;
  deleg_types;
  disable_log_facility;
  display_fsinfo;
  display_len_cat;
  display_opaque_value_max;
  display_opaque_bytes_flags;
  display_sockaddr;
  display_sockaddr_port;
  display_start;
  display_vprintf;
  DisplayLogComponentLevel;
  enable_log_facility;
  encode_fsid;
  err_type;
  err_type_str;
  export_admin_counter;
  fd_lru_pkginit;
  fd_lru_pkgshutdown;
  fgetxattr;
  find_unused_blocks;
  flistxattr;
  fhlink;
  fhreadlink;
  free_export_ops;
  fremovexattr;
  fridgethr_cancel;
  fridgethr_destroy;
  fridgethr_init;
  fridgethr_submit;
  fridgethr_sync_command;
  fs_lock;
  fsal_acl_2_posix_acl;
  fsal_acl_support;
  fsal_attach_export;
  fsal_acl_to_mode;
  fsal_common_is_referral;
  fsal_complete_fd_work;
  fsal_complete_io;
  insert_fd_lru;
  bump_fd_lru;
  remove_fd_lru;
  fsal_create;
  fsal_default_obj_ops_init;
  fsal_detach_export;
  fsal_export_init;
  fsal_export_stack;
  fsal_inherit_acls;
  fsal_init;
  fsal_link;
  fsal_listxattr_helper;
  fsal_lookup;
  fsal_can_reuse_mode_to_acl;
  fsal_mode_to_acl;
  fsal_obj_handle_init;
  fsal_obj_handle_fini;
  fsal_open2;
  fsal_print_acl_int;
  open_fd_count;
  fsal_pnfs_ds_fini;
  fsal_read;
  fsal_read2;
  fsal_readdir;
  fsal_readlink;
  fsal_remove;
  fsal_rename;
  fsal_reopen2;
  fsal_restore_ganesha_credentials;
  fsal_setattr;
  fsal_set_credentials;
  fsal_set_credentials_only_one_user;
  fsal_start_fd_work;
  fsal_start_global_io;
  fsal_start_io;
  fsal_supported_attrs;
  fsal_test_access;
  fsal_write;
  fsal2posix_openflags;
  fsal2unix_mode;
  FSAL_encode_file_layout;
  FSAL_encode_v4_multipath;
  fsetxattr;
  getfhat;
  get_fs_first_export_ref;
  get_gsh_export;
  get_optional_attrs;
  general_fridge;
  gsh_dbus_append_timestamp;
  gsh_dbus_register_path;
  gsh_dbus_broadcast;
  gsh_dbus_status_reply;
  gsh_refstr_alloc;
  gsh_refstr_put;
  gsh_refstr_release;
  g_max_files_delegatable;
  g_nodeid;
  hashtable_deletelatched;
  hashtable_for_each;
  hashtable_getlatch;
  hashtable_init;
  hashtable_destroy;
  hashtable_releaselatched;
  hashtable_setlatched;
  hashtable_test_and_set;
  ht_confirmed_client_id;
  init_error_type;
  init_op_context;
  init_op_context_simple;
  init_server_pkgs;
  insert_fd_lru;
  is_filesystem_exported;
  load_config_from_node;
  load_config_from_parse;
  log_attrlist;
  lookup_dev;
  lookup_fsal;
  lookup_fsid;
  lookup_fsid_locked;
  LogMallocFailure;
  mdcache_lru_release_entries;
  mdcache_param;
  merge_share;
  monitoring_worker_thread_max_increment;
  monitoring_worker_thread_min_increment;
  msg_fsal_err;
  nfs_notify_grace_waiters;
  nfs4_ace_alloc;
  nfs4_ace_free;
  nfs4_acl_entry_inc_ref;
  nfs4_acl_new_entry;
  nfs4_Compound_FreeOne;
  nfs4_export_check_access;
  nfs4_fs_locations_get_ref;
  nfs4_fs_locations_new;
  nfs4_Fattr_To_FSAL_attr;
  nfs4_Fattr_To_fsinfo;
  nfs4_Fattr_Free;
  nfs4_FSALattr_To_Fattr;
  nfs4_FSALToFhandle;
  nfs4_recovery_init;
  nfs4_acl_release_entry;
  nfs4_fs_locations_release;
  nfs4_op_link;
  nfs4_op_lookup;
  nfs4_op_putfh;
  nfs4_op_rename;
  nfs_config_path;
  nfs_export_get_root_entry;
  nfs_grace_is_member;
  nfs_init_init;
  nfs_init_wait;
  nfs_init_wait_timeout;
  nfs_libmain;
  nfs_param;
  nfs_pidfile_path;
  nfs_prereq_destroy;
  nfs_prereq_init;
  nfs_recovery_get_nodeid;
  nfs_ServerBootTime;
  nfs_ServerEpoch;
  nfs_set_param_from_conf;
  nfs_start;
  nfs_start_grace;
  nfs_wait_for_grace_enforcement;
  nfsop4_to_str;
  nfsproc3_to_str;
  nfsstat3_to_str;
  nfsstat4_to_str;
  nfs_get_evchannel_id;
  noop_conf_commit;
  noop_conf_init;
  object_file_type_to_str;
  op_ctx;
  open_dir_by_path_walk;
  pnfs_ds_insert;
  pnfs_ds_put;
  pnfs_ds_remove;
  posix2fsal_attributes;
  posix2fsal_attributes_all;
  posix2fsal_devt;
  posix2fsal_error;
  posix2fsal_fsid;
  posix2fsal_type;
  posix2nfs4_error;
  posix_acl_2_fsal_acl;
  posix_acl_2_xattr;
  posix_acl_entries_count;
  posix_acl_xattr_size;
  ReadDataServers;
  ReadExports;
  reaper_wake;
  RegisterCleanup;
  register_fsal;
  register_url_provider;
  release_log_facility;
  release_op_context;
  release_posix_file_system;
  read_log_config;
  report_config_errors;
  resume_op_context;
  claim_posix_filesystems;
  resolve_posix_filesystem;
  populate_posix_file_systems;
  ReturnLevelAscii;
  re_index_fs_fsid;
  root_op_export_set;
  root_op_export_options;
  set_common_verifier;
  set_const_log_str;
  set_op_context_export_fsal;
  set_saved_entry;
  SetNameFunction;
  sprint_sockip;
  start_fsals;
  state_err_str;
  strlcpy;
  subfsal_commit;
  suspend_op_context;
  to_vfs_dirent;
  unclaim_all_export_maps;
  unclaim_fs;
  unix2fsal_mode;
  unregister_fsal;
  up_async_layoutrecall;
  up_async_lock_avail;
  up_async_lock_grant;
  up_async_notify_device;
  up_async_update;
  update_export;
  update_share_counters;
  up_async_delegrecall;
  up_ready_cancel;
  up_ready_wait;
  vfs_readents;
  vfs_utimes;
  vfs_utimesat;
  xattr_2_posix_acl;
  xdr_io_data;
  xdr_notify;
  xdr_READ4res_uio_setup;
  _get_gsh_export_ref;
  _put_gsh_export;
  __tracepoint_fsalmem___mem_free;
  __tracepoint_fsalmem___mem_alloc_state;
  __tracepoint_fsalmem___mem_inuse;
  __tracepoint_fsalmem___mem_get_ref;
  __tracepoint_fsalmem___mem_put_ref;
  __tracepoint_fsalmem___mem_alloc;
  __tracepoint_fsalmem___mem_lookup;
  __tracepoint_fsalmem___mem_readdir;
  __tracepoint_fsalmem___mem_mkdir;
  __tracepoint_fsalmem___mem_getattrs;
  __tracepoint_fsalmem___mem_setattrs;
  __tracepoint_fsalmem___mem_link;
  __tracepoint_fsalmem___mem_unlink;
  __tracepoint_fsalmem___mem_rename;
  __tracepoint_fsalmem___mem_open;
  __tracepoint_fsalmem___mem_read;
  __tracepoint_fsalmem___mem_write;
  __tracepoint_fsalmem___mem_close;
  __tracepoint_fsalmem___mem_create_handle;
  __tracepoint_fsalgl___open_fd;
  __tracepoint_fsalgl___close_fd;
  __tracepoint_fsalgl___gl_handle;
  __tracepoint_fsalceph___ceph_create_handle;
  __tracepoint_fsalceph___ceph_close;
  __tracepoint_fsalceph___ceph_commit;
  __tracepoint_fsalceph___ceph_falloc;
  __tracepoint_fsalceph___ceph_getattrs;
  __tracepoint_fsalceph___ceph_lease;
  __tracepoint_fsalceph___ceph_lock;
  __tracepoint_fsalceph___ceph_lookup;
  __tracepoint_fsalceph___ceph_mkdir;
  __tracepoint_fsalceph___ceph_mknod;
  __tracepoint_fsalceph___ceph_open;
  __tracepoint_fsalceph___ceph_read;
  __tracepoint_fsalceph___ceph_readdir;
  __tracepoint_fsalceph___ceph_setattrs;
  __tracepoint_fsalceph___ceph_unlink;
  __tracepoint_fsalceph___ceph_write;
local:
  *;
};
