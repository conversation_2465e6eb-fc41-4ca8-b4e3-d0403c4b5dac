#!/bin/bash
#
# chkconfig: 2345 50 50
# description: GANESHA NFS Daemon
#
# processname: /usr/bin/ganesha.nfsd
# config: /etc/ganesha/ganesha.nfsd.conf
# pidfile: /var/run/ganesha.nfsd.pid
#
### BEGIN INIT INFO
# Provides: ganesha
# Required-Start: $local_fs $named $time $network
# Required-Stop: $local_fs $named $time $network
# Default-Start: 2 3 4 5
# Default-Stop: 0 1 6
# Short-Description: start and stop ganesha daemon
# Description: NFS-GANESHA
### END INIT INFO


# source function library
. /etc/rc.d/init.d/functions

PATHPROG=/usr/bin/ganesha.nfsd

# Default Ganesha options
LOGFILE=/var/log/ganesha/ganesha.log
CONFFILE=/etc/ganesha/ganesha.conf

prog=ganesha.nfsd
PID_FILE=${PID_FILE:=/var/run/${prog}.pid}
LOCK_FILE=${LOCK_FILE:=/var/lock/subsys/${prog}}


# Generate and read config values
[ -x /usr/libexec/ganesha/nfs-ganesha-config.sh ] && /usr/libexec/ganesha/nfs-ganesha-config.sh
[ -f /run/sysconfig/ganesha ] && . /run/sysconfig/ganesha

[ -z "$NOFILE" ] && NOFILE=1048576

OPTIONS="-L $LOGFILE -f $CONFFILE -N NIV_EVENT -p $PID_FILE $EPOCH"
RETVAL=0


start() {
        echo -n $"Starting $prog: "
        if [ $UID -ne 0 ]; then
                RETVAL=1
                failure
        else
		mkdir -p "$(dirname "$PID_FILE")"
		ulimit -n $NOFILE
                daemon --pidfile $PID_FILE $PATHPROG $OPTIONS
                RETVAL=$?
                if [ $RETVAL -eq 0 ]; then
		            touch $LOCK_FILE
		        else
		            RETVAL=1
                fi
        fi
        echo
}

stop() {
        echo -n $"Stopping $prog: "
        if [ $UID -ne 0 ]; then
                RETVAL=1
                failure
        else
                killproc $PATHPROG
                RETVAL=$?
                if [ $RETVAL -eq 0 ]; then
		  rm -f  $LOCK_FILE
                  success
		else
		  failure
                fi

        fi
        echo
        return $RETVAL
}

case "$1" in
  start)
	start
	;;
  stop)
	stop
	;;
  restart)
	if [ -f $LOCK_FILE ] ; then
		stop
		#avoid race
    	sleep 3
	fi
	start
        ;;
  reload)
	echo -n $"Reloading $prog: "
	if [ -f $LOCK_FILE ] ; then
		killproc -p $PID_FILE $prog -HUP
		RETVAL=0
	else
		RETVAL=1
	fi
        echo
        ;;
  force-reload)
	stop
	start
	;;
  try-restart)
	if [ -f $LOCK_FILE ] ; then
		stop

		#avoid race
		sleep 3
		start
	fi
	;;
  status)
	if [ $RETVAL -eq 5 ] ; then
	    RETVAL=3
        else
	   status -p $PID_FILE $PATHPROG
	   RETVAL=$?
        fi
	;;
  *)
	echo $"Usage: $0 {start|stop|restart|reload|try-restart|status}"
	RETVAL=1
esac

exit $RETVAL
