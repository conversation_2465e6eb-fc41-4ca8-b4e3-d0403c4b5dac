#
# Fichier de configuration de test 
#
# $Header: /cea/home/<USER>/cvs/SHERPA/BaseCvs/GANESHA/src/Log/maketest.conf,v 1.4 2005/02/17 12:58:28 leibovic Exp $
# 
# $Log: maketest.conf,v $
# Revision 1.4  2005/02/17 12:58:28  leibovic
# Added multhithread test.
#
# Revision 1.3  2004/11/18 10:36:20  deniel
# Dependence corrected in the GNUmakefiles
#
# Revision 1.2  2004/10/22 09:24:30  deniel
# No more dynamic libraries compiled
#
# Revision 1.1  2004/09/30 14:08:42  deniel
# Ajout des log en anglais (a partir des logs aglae)
#
#
#

Test Test_liblog_Standard
{
   Product = Static version of the log library
   Command = ./test_liblog_STD.sh
   Comment = Check for correctness of the log

        Failure BadStatus
        {
          STATUS != 0 
        }
        
        Success TestOk
        {
          STDOUT =~ /PASSED/
            AND
          STATUS == 0 
        }

}

Test Test_liblog_Multithread
{
   Product = Static version of the log library
   Command = ./test_liblog_MT.sh 
   Comment = Check for correctness of the log

        Failure BadStatus
        {
          STATUS != 0 
        }
        
        Success TestOk
        {
          STATUS == 0 
        }

}

