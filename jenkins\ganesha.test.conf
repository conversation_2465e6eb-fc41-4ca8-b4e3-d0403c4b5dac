###################################################
#     Export entries
###################################################

EXPORT
{
  
  # Export Id (mandatory)
  Export_Id = 77 ;
  
  # Exported path (mandatory)
  Path = "/tmp" ;
 
  Root_Access = "*" ;
  
  Access = "*";
  
  # Pseudo path for NFSv4 export (mandatory)
  Pseudo = "/tmp";
  
  SecType = "sys";
 
  # The uid for root when its host doesn't have a root_access. (default: -2)
  Anonymous_root_uid = -2 ;
   
  NFS_Protocols = "3,4" ;
  
  SecType = "sys";
  
  # Maximum size for a read operation.
  MaxRead = 32768;
  
  # Maximum size for a write operation.
  MaxWrite = 32768;
  
  # Preferred size for a read operation.
  PrefRead = 32768;
  
  # Preferred size for a write operation.
  PrefWrite = 32768;
  
  # Preferred size for a readdir operation.
  PrefReaddir = 32768;

  # Filesystem ID (default  666.666)
  # This sets the filesystem id for the entries of this export.
  Filesystem_id = 192.168 ;
 
  # Should the client to this export entry come from a privileged port ?	
  PrivilegedPort = FALSE ;

  # Export entry "tag" name
  # Can be used as an alternative way of addressing the
  # export entry at mount time ( alternate to the 'Path')
  Tag = "vfs" ;
 
  FSAL = "VFS" ;
}

FSAL
{
  
  
  VFS
   {
      FSAL_Shared_Library = /tmp/libfsalvfs.so.4.2.0 ;
   }

}

FileSystem
{ 
  
  Umask = 0000 ;
  
  
  Link_support = TRUE;     # hardlink support
  Symlink_support = TRUE;  # symlinks support
  CanSetTime = TRUE;       # Is it possible to change file times
  
}



NFS_Core_Param
{
	# Number of worker threads to be used
	Nb_Worker = 60 ;

	# NFS Port to be used 
	# Default value is 2049
	NFS_Port = 2049 ;

	# Mount port to be used 
	# Default is 0 (let the system use an available ephemeral port)
	#MNT_Port = 0 ;
	
	# NFS RPC Program number 
	# Default value is 100003
	#NFS_Program = 100003 ;

	# Mount protocol RPC Program Number
	# Default value is 100005
	#MNT_Program = 100005 ;

        NFS_Protocols = "3,4" ;

	# Size to be used for the core dump file (if the daemon crashes)
        ##Core_Dump_Size = 0 ;

}


_9P
{
     _9P_TCP_Port = 564 ;
     _9P_RDMA_Port = 5640 ;

      DebugLevel = NIV_DEBUG ;
     #
 
}
