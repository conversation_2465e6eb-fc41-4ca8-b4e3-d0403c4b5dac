# Implementation of same file concurrent locks with varied ranges
#
# Take 128 locks on a file with small ranges and none gaps
# small = 4 * size.KiB
# Check to see that those locks exists
# Release the locks
# Check to see that those locks don’t exists

CLIENTS c1 c2

OK c1 OPEN 1 rw create POSIX testFile
OK c2 OPEN 1 rw POSIX testFile

GRANTED c1 LOCK 1 read 0 4096
GRANTED c1 LOCK 1 read 4096 8192
GRANTED c1 LOCK 1 read 8192 12288
GRANTED c1 LOCK 1 read 12288 16384
GRANTED c1 LOCK 1 read 16384 20480
GRANTED c1 LOCK 1 read 20480 24576
GRANTED c1 LOCK 1 read 24576 28672
GRANTED c1 LOCK 1 read 28672 32768
GRANTED c1 LOCK 1 read 32768 36864
GRANTED c1 LOCK 1 read 36864 40960
GRANTED c1 LOCK 1 read 40960 45056
GRANTED c1 LOCK 1 read 45056 49152
GRANTED c1 LOCK 1 read 49152 53248
GRANTED c1 LOCK 1 read 53248 57344
GRANTED c1 LOCK 1 read 57344 61440
GRANTED c1 LOCK 1 read 61440 65536
GRANTED c1 LOCK 1 read 65536 69632
GRANTED c1 LOCK 1 read 69632 73728
GRANTED c1 LOCK 1 read 73728 77824
GRANTED c1 LOCK 1 read 77824 81920
GRANTED c1 LOCK 1 read 81920 86016
GRANTED c1 LOCK 1 read 86016 90112
GRANTED c1 LOCK 1 read 90112 94208
GRANTED c1 LOCK 1 read 94208 98304
GRANTED c1 LOCK 1 read 98304 102400
GRANTED c1 LOCK 1 read 102400 106496
GRANTED c1 LOCK 1 read 106496 110592
GRANTED c1 LOCK 1 read 110592 114688
GRANTED c1 LOCK 1 read 114688 118784
GRANTED c1 LOCK 1 read 118784 122880
GRANTED c1 LOCK 1 read 122880 126976
GRANTED c1 LOCK 1 read 126976 131072
GRANTED c1 LOCK 1 read 131072 135168
GRANTED c1 LOCK 1 read 135168 139264
GRANTED c1 LOCK 1 read 139264 143360
GRANTED c1 LOCK 1 read 143360 147456
GRANTED c1 LOCK 1 read 147456 151552
GRANTED c1 LOCK 1 read 151552 155648
GRANTED c1 LOCK 1 read 155648 159744
GRANTED c1 LOCK 1 read 159744 163840
GRANTED c1 LOCK 1 read 163840 167936
GRANTED c1 LOCK 1 read 167936 172032
GRANTED c1 LOCK 1 read 172032 176128
GRANTED c1 LOCK 1 read 176128 180224
GRANTED c1 LOCK 1 read 180224 184320
GRANTED c1 LOCK 1 read 184320 188416
GRANTED c1 LOCK 1 read 188416 192512
GRANTED c1 LOCK 1 read 192512 196608
GRANTED c1 LOCK 1 read 196608 200704
GRANTED c1 LOCK 1 read 200704 204800
GRANTED c1 LOCK 1 read 204800 208896
GRANTED c1 LOCK 1 read 208896 212992
GRANTED c1 LOCK 1 read 212992 217088
GRANTED c1 LOCK 1 read 217088 221184
GRANTED c1 LOCK 1 read 221184 225280
GRANTED c1 LOCK 1 read 225280 229376
GRANTED c1 LOCK 1 read 229376 233472
GRANTED c1 LOCK 1 read 233472 237568
GRANTED c1 LOCK 1 read 237568 241664
GRANTED c1 LOCK 1 read 241664 245760
GRANTED c1 LOCK 1 read 245760 249856
GRANTED c1 LOCK 1 read 249856 253952
GRANTED c1 LOCK 1 read 253952 258048
GRANTED c1 LOCK 1 read 258048 262144
GRANTED c1 LOCK 1 read 262144 266240
GRANTED c1 LOCK 1 read 266240 270336
GRANTED c1 LOCK 1 read 270336 274432
GRANTED c1 LOCK 1 read 274432 278528
GRANTED c1 LOCK 1 read 278528 282624
GRANTED c1 LOCK 1 read 282624 286720
GRANTED c1 LOCK 1 read 286720 290816
GRANTED c1 LOCK 1 read 290816 294912
GRANTED c1 LOCK 1 read 294912 299008
GRANTED c1 LOCK 1 read 299008 303104
GRANTED c1 LOCK 1 read 303104 307200
GRANTED c1 LOCK 1 read 307200 311296
GRANTED c1 LOCK 1 read 311296 315392
GRANTED c1 LOCK 1 read 315392 319488
GRANTED c1 LOCK 1 read 319488 323584
GRANTED c1 LOCK 1 read 323584 327680
GRANTED c1 LOCK 1 read 327680 331776
GRANTED c1 LOCK 1 read 331776 335872
GRANTED c1 LOCK 1 read 335872 339968
GRANTED c1 LOCK 1 read 339968 344064
GRANTED c1 LOCK 1 read 344064 348160
GRANTED c1 LOCK 1 read 348160 352256
GRANTED c1 LOCK 1 read 352256 356352
GRANTED c1 LOCK 1 read 356352 360448
GRANTED c1 LOCK 1 read 360448 364544
GRANTED c1 LOCK 1 read 364544 368640
GRANTED c1 LOCK 1 read 368640 372736
GRANTED c1 LOCK 1 read 372736 376832
GRANTED c1 LOCK 1 read 376832 380928
GRANTED c1 LOCK 1 read 380928 385024
GRANTED c1 LOCK 1 read 385024 389120
GRANTED c1 LOCK 1 read 389120 393216
GRANTED c1 LOCK 1 read 393216 397312
GRANTED c1 LOCK 1 read 397312 401408
GRANTED c1 LOCK 1 read 401408 405504
GRANTED c1 LOCK 1 read 405504 409600
GRANTED c1 LOCK 1 read 409600 413696
GRANTED c1 LOCK 1 read 413696 417792
GRANTED c1 LOCK 1 read 417792 421888
GRANTED c1 LOCK 1 read 421888 425984
GRANTED c1 LOCK 1 read 425984 430080
GRANTED c1 LOCK 1 read 430080 434176
GRANTED c1 LOCK 1 read 434176 438272
GRANTED c1 LOCK 1 read 438272 442368
GRANTED c1 LOCK 1 read 442368 446464
GRANTED c1 LOCK 1 read 446464 450560
GRANTED c1 LOCK 1 read 450560 454656
GRANTED c1 LOCK 1 read 454656 458752
GRANTED c1 LOCK 1 read 458752 462848
GRANTED c1 LOCK 1 read 462848 466944
GRANTED c1 LOCK 1 read 466944 471040
GRANTED c1 LOCK 1 read 471040 475136
GRANTED c1 LOCK 1 read 475136 479232
GRANTED c1 LOCK 1 read 479232 483328
GRANTED c1 LOCK 1 read 483328 487424
GRANTED c1 LOCK 1 read 487424 491520
GRANTED c1 LOCK 1 read 491520 495616
GRANTED c1 LOCK 1 read 495616 499712
GRANTED c1 LOCK 1 read 499712 503808
GRANTED c1 LOCK 1 read 503808 507904
GRANTED c1 LOCK 1 read 507904 512000
GRANTED c1 LOCK 1 read 512000 516096
GRANTED c1 LOCK 1 read 516096 520192
GRANTED c1 LOCK 1 read 520192 524288

c2 $L LIST 1 0 0
{
  EXPECT c2 $L LIST CONFLICT 1 * read 0 4096
  EXPECT c2 $L LIST CONFLICT 1 * read 4096 8192
  EXPECT c2 $L LIST CONFLICT 1 * read 8192 12288
  EXPECT c2 $L LIST CONFLICT 1 * read 12288 16384
  EXPECT c2 $L LIST CONFLICT 1 * read 16384 20480
  EXPECT c2 $L LIST CONFLICT 1 * read 20480 24576
  EXPECT c2 $L LIST CONFLICT 1 * read 24576 28672
  EXPECT c2 $L LIST CONFLICT 1 * read 28672 32768
  EXPECT c2 $L LIST CONFLICT 1 * read 32768 36864
  EXPECT c2 $L LIST CONFLICT 1 * read 36864 40960
  EXPECT c2 $L LIST CONFLICT 1 * read 40960 45056
  EXPECT c2 $L LIST CONFLICT 1 * read 45056 49152
  EXPECT c2 $L LIST CONFLICT 1 * read 49152 53248
  EXPECT c2 $L LIST CONFLICT 1 * read 53248 57344
  EXPECT c2 $L LIST CONFLICT 1 * read 57344 61440
  EXPECT c2 $L LIST CONFLICT 1 * read 61440 65536
  EXPECT c2 $L LIST CONFLICT 1 * read 65536 69632
  EXPECT c2 $L LIST CONFLICT 1 * read 69632 73728
  EXPECT c2 $L LIST CONFLICT 1 * read 73728 77824
  EXPECT c2 $L LIST CONFLICT 1 * read 77824 81920
  EXPECT c2 $L LIST CONFLICT 1 * read 81920 86016
  EXPECT c2 $L LIST CONFLICT 1 * read 86016 90112
  EXPECT c2 $L LIST CONFLICT 1 * read 90112 94208
  EXPECT c2 $L LIST CONFLICT 1 * read 94208 98304
  EXPECT c2 $L LIST CONFLICT 1 * read 98304 102400
  EXPECT c2 $L LIST CONFLICT 1 * read 102400 106496
  EXPECT c2 $L LIST CONFLICT 1 * read 106496 110592
  EXPECT c2 $L LIST CONFLICT 1 * read 110592 114688
  EXPECT c2 $L LIST CONFLICT 1 * read 114688 118784
  EXPECT c2 $L LIST CONFLICT 1 * read 118784 122880
  EXPECT c2 $L LIST CONFLICT 1 * read 122880 126976
  EXPECT c2 $L LIST CONFLICT 1 * read 126976 131072
  EXPECT c2 $L LIST CONFLICT 1 * read 131072 135168
  EXPECT c2 $L LIST CONFLICT 1 * read 135168 139264
  EXPECT c2 $L LIST CONFLICT 1 * read 139264 143360
  EXPECT c2 $L LIST CONFLICT 1 * read 143360 147456
  EXPECT c2 $L LIST CONFLICT 1 * read 147456 151552
  EXPECT c2 $L LIST CONFLICT 1 * read 151552 155648
  EXPECT c2 $L LIST CONFLICT 1 * read 155648 159744
  EXPECT c2 $L LIST CONFLICT 1 * read 159744 163840
  EXPECT c2 $L LIST CONFLICT 1 * read 163840 167936
  EXPECT c2 $L LIST CONFLICT 1 * read 167936 172032
  EXPECT c2 $L LIST CONFLICT 1 * read 172032 176128
  EXPECT c2 $L LIST CONFLICT 1 * read 176128 180224
  EXPECT c2 $L LIST CONFLICT 1 * read 180224 184320
  EXPECT c2 $L LIST CONFLICT 1 * read 184320 188416
  EXPECT c2 $L LIST CONFLICT 1 * read 188416 192512
  EXPECT c2 $L LIST CONFLICT 1 * read 192512 196608
  EXPECT c2 $L LIST CONFLICT 1 * read 196608 200704
  EXPECT c2 $L LIST CONFLICT 1 * read 200704 204800
  EXPECT c2 $L LIST CONFLICT 1 * read 204800 208896
  EXPECT c2 $L LIST CONFLICT 1 * read 208896 212992
  EXPECT c2 $L LIST CONFLICT 1 * read 212992 217088
  EXPECT c2 $L LIST CONFLICT 1 * read 217088 221184
  EXPECT c2 $L LIST CONFLICT 1 * read 221184 225280
  EXPECT c2 $L LIST CONFLICT 1 * read 225280 229376
  EXPECT c2 $L LIST CONFLICT 1 * read 229376 233472
  EXPECT c2 $L LIST CONFLICT 1 * read 233472 237568
  EXPECT c2 $L LIST CONFLICT 1 * read 237568 241664
  EXPECT c2 $L LIST CONFLICT 1 * read 241664 245760
  EXPECT c2 $L LIST CONFLICT 1 * read 245760 249856
  EXPECT c2 $L LIST CONFLICT 1 * read 249856 253952
  EXPECT c2 $L LIST CONFLICT 1 * read 253952 258048
  EXPECT c2 $L LIST CONFLICT 1 * read 258048 262144
  EXPECT c2 $L LIST CONFLICT 1 * read 262144 266240
  EXPECT c2 $L LIST CONFLICT 1 * read 266240 270336
  EXPECT c2 $L LIST CONFLICT 1 * read 270336 274432
  EXPECT c2 $L LIST CONFLICT 1 * read 274432 278528
  EXPECT c2 $L LIST CONFLICT 1 * read 278528 282624
  EXPECT c2 $L LIST CONFLICT 1 * read 282624 286720
  EXPECT c2 $L LIST CONFLICT 1 * read 286720 290816
  EXPECT c2 $L LIST CONFLICT 1 * read 290816 294912
  EXPECT c2 $L LIST CONFLICT 1 * read 294912 299008
  EXPECT c2 $L LIST CONFLICT 1 * read 299008 303104
  EXPECT c2 $L LIST CONFLICT 1 * read 303104 307200
  EXPECT c2 $L LIST CONFLICT 1 * read 307200 311296
  EXPECT c2 $L LIST CONFLICT 1 * read 311296 315392
  EXPECT c2 $L LIST CONFLICT 1 * read 315392 319488
  EXPECT c2 $L LIST CONFLICT 1 * read 319488 323584
  EXPECT c2 $L LIST CONFLICT 1 * read 323584 327680
  EXPECT c2 $L LIST CONFLICT 1 * read 327680 331776
  EXPECT c2 $L LIST CONFLICT 1 * read 331776 335872
  EXPECT c2 $L LIST CONFLICT 1 * read 335872 339968
  EXPECT c2 $L LIST CONFLICT 1 * read 339968 344064
  EXPECT c2 $L LIST CONFLICT 1 * read 344064 348160
  EXPECT c2 $L LIST CONFLICT 1 * read 348160 352256
  EXPECT c2 $L LIST CONFLICT 1 * read 352256 356352
  EXPECT c2 $L LIST CONFLICT 1 * read 356352 360448
  EXPECT c2 $L LIST CONFLICT 1 * read 360448 364544
  EXPECT c2 $L LIST CONFLICT 1 * read 364544 368640
  EXPECT c2 $L LIST CONFLICT 1 * read 368640 372736
  EXPECT c2 $L LIST CONFLICT 1 * read 372736 376832
  EXPECT c2 $L LIST CONFLICT 1 * read 376832 380928
  EXPECT c2 $L LIST CONFLICT 1 * read 380928 385024
  EXPECT c2 $L LIST CONFLICT 1 * read 385024 389120
  EXPECT c2 $L LIST CONFLICT 1 * read 389120 393216
  EXPECT c2 $L LIST CONFLICT 1 * read 393216 397312
  EXPECT c2 $L LIST CONFLICT 1 * read 397312 401408
  EXPECT c2 $L LIST CONFLICT 1 * read 401408 405504
  EXPECT c2 $L LIST CONFLICT 1 * read 405504 409600
  EXPECT c2 $L LIST CONFLICT 1 * read 409600 413696
  EXPECT c2 $L LIST CONFLICT 1 * read 413696 417792
  EXPECT c2 $L LIST CONFLICT 1 * read 417792 421888
  EXPECT c2 $L LIST CONFLICT 1 * read 421888 425984
  EXPECT c2 $L LIST CONFLICT 1 * read 425984 430080
  EXPECT c2 $L LIST CONFLICT 1 * read 430080 434176
  EXPECT c2 $L LIST CONFLICT 1 * read 434176 438272
  EXPECT c2 $L LIST CONFLICT 1 * read 438272 442368
  EXPECT c2 $L LIST CONFLICT 1 * read 442368 446464
  EXPECT c2 $L LIST CONFLICT 1 * read 446464 450560
  EXPECT c2 $L LIST CONFLICT 1 * read 450560 454656
  EXPECT c2 $L LIST CONFLICT 1 * read 454656 458752
  EXPECT c2 $L LIST CONFLICT 1 * read 458752 462848
  EXPECT c2 $L LIST CONFLICT 1 * read 462848 466944
  EXPECT c2 $L LIST CONFLICT 1 * read 466944 471040
  EXPECT c2 $L LIST CONFLICT 1 * read 471040 475136
  EXPECT c2 $L LIST CONFLICT 1 * read 475136 479232
  EXPECT c2 $L LIST CONFLICT 1 * read 479232 483328
  EXPECT c2 $L LIST CONFLICT 1 * read 483328 487424
  EXPECT c2 $L LIST CONFLICT 1 * read 487424 491520
  EXPECT c2 $L LIST CONFLICT 1 * read 491520 495616
  EXPECT c2 $L LIST CONFLICT 1 * read 495616 499712
  EXPECT c2 $L LIST CONFLICT 1 * read 499712 503808
  EXPECT c2 $L LIST CONFLICT 1 * read 503808 507904
  EXPECT c2 $L LIST CONFLICT 1 * read 507904 512000
  EXPECT c2 $L LIST CONFLICT 1 * read 512000 516096
  EXPECT c2 $L LIST CONFLICT 1 * read 516096 520192
  EXPECT c2 $L LIST CONFLICT 1 * read 520192 524288
}
EXPECT c2 $L LIST DENIED 1 0 0
GRANTED c1 UNLOCK 1 0 0
AVAILABLE c2 LIST 1 0 0

GRANTED c1 LOCK 1 write 0 4096
GRANTED c1 LOCK 1 write 4096 8192
GRANTED c1 LOCK 1 write 8192 12288
GRANTED c1 LOCK 1 write 12288 16384
GRANTED c1 LOCK 1 write 16384 20480
GRANTED c1 LOCK 1 write 20480 24576
GRANTED c1 LOCK 1 write 24576 28672
GRANTED c1 LOCK 1 write 28672 32768
GRANTED c1 LOCK 1 write 32768 36864
GRANTED c1 LOCK 1 write 36864 40960
GRANTED c1 LOCK 1 write 40960 45056
GRANTED c1 LOCK 1 write 45056 49152
GRANTED c1 LOCK 1 write 49152 53248
GRANTED c1 LOCK 1 write 53248 57344
GRANTED c1 LOCK 1 write 57344 61440
GRANTED c1 LOCK 1 write 61440 65536
GRANTED c1 LOCK 1 write 65536 69632
GRANTED c1 LOCK 1 write 69632 73728
GRANTED c1 LOCK 1 write 73728 77824
GRANTED c1 LOCK 1 write 77824 81920
GRANTED c1 LOCK 1 write 81920 86016
GRANTED c1 LOCK 1 write 86016 90112
GRANTED c1 LOCK 1 write 90112 94208
GRANTED c1 LOCK 1 write 94208 98304
GRANTED c1 LOCK 1 write 98304 102400
GRANTED c1 LOCK 1 write 102400 106496
GRANTED c1 LOCK 1 write 106496 110592
GRANTED c1 LOCK 1 write 110592 114688
GRANTED c1 LOCK 1 write 114688 118784
GRANTED c1 LOCK 1 write 118784 122880
GRANTED c1 LOCK 1 write 122880 126976
GRANTED c1 LOCK 1 write 126976 131072
GRANTED c1 LOCK 1 write 131072 135168
GRANTED c1 LOCK 1 write 135168 139264
GRANTED c1 LOCK 1 write 139264 143360
GRANTED c1 LOCK 1 write 143360 147456
GRANTED c1 LOCK 1 write 147456 151552
GRANTED c1 LOCK 1 write 151552 155648
GRANTED c1 LOCK 1 write 155648 159744
GRANTED c1 LOCK 1 write 159744 163840
GRANTED c1 LOCK 1 write 163840 167936
GRANTED c1 LOCK 1 write 167936 172032
GRANTED c1 LOCK 1 write 172032 176128
GRANTED c1 LOCK 1 write 176128 180224
GRANTED c1 LOCK 1 write 180224 184320
GRANTED c1 LOCK 1 write 184320 188416
GRANTED c1 LOCK 1 write 188416 192512
GRANTED c1 LOCK 1 write 192512 196608
GRANTED c1 LOCK 1 write 196608 200704
GRANTED c1 LOCK 1 write 200704 204800
GRANTED c1 LOCK 1 write 204800 208896
GRANTED c1 LOCK 1 write 208896 212992
GRANTED c1 LOCK 1 write 212992 217088
GRANTED c1 LOCK 1 write 217088 221184
GRANTED c1 LOCK 1 write 221184 225280
GRANTED c1 LOCK 1 write 225280 229376
GRANTED c1 LOCK 1 write 229376 233472
GRANTED c1 LOCK 1 write 233472 237568
GRANTED c1 LOCK 1 write 237568 241664
GRANTED c1 LOCK 1 write 241664 245760
GRANTED c1 LOCK 1 write 245760 249856
GRANTED c1 LOCK 1 write 249856 253952
GRANTED c1 LOCK 1 write 253952 258048
GRANTED c1 LOCK 1 write 258048 262144
GRANTED c1 LOCK 1 write 262144 266240
GRANTED c1 LOCK 1 write 266240 270336
GRANTED c1 LOCK 1 write 270336 274432
GRANTED c1 LOCK 1 write 274432 278528
GRANTED c1 LOCK 1 write 278528 282624
GRANTED c1 LOCK 1 write 282624 286720
GRANTED c1 LOCK 1 write 286720 290816
GRANTED c1 LOCK 1 write 290816 294912
GRANTED c1 LOCK 1 write 294912 299008
GRANTED c1 LOCK 1 write 299008 303104
GRANTED c1 LOCK 1 write 303104 307200
GRANTED c1 LOCK 1 write 307200 311296
GRANTED c1 LOCK 1 write 311296 315392
GRANTED c1 LOCK 1 write 315392 319488
GRANTED c1 LOCK 1 write 319488 323584
GRANTED c1 LOCK 1 write 323584 327680
GRANTED c1 LOCK 1 write 327680 331776
GRANTED c1 LOCK 1 write 331776 335872
GRANTED c1 LOCK 1 write 335872 339968
GRANTED c1 LOCK 1 write 339968 344064
GRANTED c1 LOCK 1 write 344064 348160
GRANTED c1 LOCK 1 write 348160 352256
GRANTED c1 LOCK 1 write 352256 356352
GRANTED c1 LOCK 1 write 356352 360448
GRANTED c1 LOCK 1 write 360448 364544
GRANTED c1 LOCK 1 write 364544 368640
GRANTED c1 LOCK 1 write 368640 372736
GRANTED c1 LOCK 1 write 372736 376832
GRANTED c1 LOCK 1 write 376832 380928
GRANTED c1 LOCK 1 write 380928 385024
GRANTED c1 LOCK 1 write 385024 389120
GRANTED c1 LOCK 1 write 389120 393216
GRANTED c1 LOCK 1 write 393216 397312
GRANTED c1 LOCK 1 write 397312 401408
GRANTED c1 LOCK 1 write 401408 405504
GRANTED c1 LOCK 1 write 405504 409600
GRANTED c1 LOCK 1 write 409600 413696
GRANTED c1 LOCK 1 write 413696 417792
GRANTED c1 LOCK 1 write 417792 421888
GRANTED c1 LOCK 1 write 421888 425984
GRANTED c1 LOCK 1 write 425984 430080
GRANTED c1 LOCK 1 write 430080 434176
GRANTED c1 LOCK 1 write 434176 438272
GRANTED c1 LOCK 1 write 438272 442368
GRANTED c1 LOCK 1 write 442368 446464
GRANTED c1 LOCK 1 write 446464 450560
GRANTED c1 LOCK 1 write 450560 454656
GRANTED c1 LOCK 1 write 454656 458752
GRANTED c1 LOCK 1 write 458752 462848
GRANTED c1 LOCK 1 write 462848 466944
GRANTED c1 LOCK 1 write 466944 471040
GRANTED c1 LOCK 1 write 471040 475136
GRANTED c1 LOCK 1 write 475136 479232
GRANTED c1 LOCK 1 write 479232 483328
GRANTED c1 LOCK 1 write 483328 487424
GRANTED c1 LOCK 1 write 487424 491520
GRANTED c1 LOCK 1 write 491520 495616
GRANTED c1 LOCK 1 write 495616 499712
GRANTED c1 LOCK 1 write 499712 503808
GRANTED c1 LOCK 1 write 503808 507904
GRANTED c1 LOCK 1 write 507904 512000
GRANTED c1 LOCK 1 write 512000 516096
GRANTED c1 LOCK 1 write 516096 520192
GRANTED c1 LOCK 1 write 520192 524288

c2 $L LIST 1 0 0
{
   EXPECT c2 $L LIST CONFLICT 1 * write 0 4096
   EXPECT c2 $L LIST CONFLICT 1 * write 4096 8192
   EXPECT c2 $L LIST CONFLICT 1 * write 8192 12288
   EXPECT c2 $L LIST CONFLICT 1 * write 12288 16384
   EXPECT c2 $L LIST CONFLICT 1 * write 16384 20480
   EXPECT c2 $L LIST CONFLICT 1 * write 20480 24576
   EXPECT c2 $L LIST CONFLICT 1 * write 24576 28672
   EXPECT c2 $L LIST CONFLICT 1 * write 28672 32768
   EXPECT c2 $L LIST CONFLICT 1 * write 32768 36864
   EXPECT c2 $L LIST CONFLICT 1 * write 36864 40960
   EXPECT c2 $L LIST CONFLICT 1 * write 40960 45056
   EXPECT c2 $L LIST CONFLICT 1 * write 45056 49152
   EXPECT c2 $L LIST CONFLICT 1 * write 49152 53248
   EXPECT c2 $L LIST CONFLICT 1 * write 53248 57344
   EXPECT c2 $L LIST CONFLICT 1 * write 57344 61440
   EXPECT c2 $L LIST CONFLICT 1 * write 61440 65536
   EXPECT c2 $L LIST CONFLICT 1 * write 65536 69632
   EXPECT c2 $L LIST CONFLICT 1 * write 69632 73728
   EXPECT c2 $L LIST CONFLICT 1 * write 73728 77824
   EXPECT c2 $L LIST CONFLICT 1 * write 77824 81920
   EXPECT c2 $L LIST CONFLICT 1 * write 81920 86016
   EXPECT c2 $L LIST CONFLICT 1 * write 86016 90112
   EXPECT c2 $L LIST CONFLICT 1 * write 90112 94208
   EXPECT c2 $L LIST CONFLICT 1 * write 94208 98304
   EXPECT c2 $L LIST CONFLICT 1 * write 98304 102400
   EXPECT c2 $L LIST CONFLICT 1 * write 102400 106496
   EXPECT c2 $L LIST CONFLICT 1 * write 106496 110592
   EXPECT c2 $L LIST CONFLICT 1 * write 110592 114688
   EXPECT c2 $L LIST CONFLICT 1 * write 114688 118784
   EXPECT c2 $L LIST CONFLICT 1 * write 118784 122880
   EXPECT c2 $L LIST CONFLICT 1 * write 122880 126976
   EXPECT c2 $L LIST CONFLICT 1 * write 126976 131072
   EXPECT c2 $L LIST CONFLICT 1 * write 131072 135168
   EXPECT c2 $L LIST CONFLICT 1 * write 135168 139264
   EXPECT c2 $L LIST CONFLICT 1 * write 139264 143360
   EXPECT c2 $L LIST CONFLICT 1 * write 143360 147456
   EXPECT c2 $L LIST CONFLICT 1 * write 147456 151552
   EXPECT c2 $L LIST CONFLICT 1 * write 151552 155648
   EXPECT c2 $L LIST CONFLICT 1 * write 155648 159744
   EXPECT c2 $L LIST CONFLICT 1 * write 159744 163840
   EXPECT c2 $L LIST CONFLICT 1 * write 163840 167936
   EXPECT c2 $L LIST CONFLICT 1 * write 167936 172032
   EXPECT c2 $L LIST CONFLICT 1 * write 172032 176128
   EXPECT c2 $L LIST CONFLICT 1 * write 176128 180224
   EXPECT c2 $L LIST CONFLICT 1 * write 180224 184320
   EXPECT c2 $L LIST CONFLICT 1 * write 184320 188416
   EXPECT c2 $L LIST CONFLICT 1 * write 188416 192512
   EXPECT c2 $L LIST CONFLICT 1 * write 192512 196608
   EXPECT c2 $L LIST CONFLICT 1 * write 196608 200704
   EXPECT c2 $L LIST CONFLICT 1 * write 200704 204800
   EXPECT c2 $L LIST CONFLICT 1 * write 204800 208896
   EXPECT c2 $L LIST CONFLICT 1 * write 208896 212992
   EXPECT c2 $L LIST CONFLICT 1 * write 212992 217088
   EXPECT c2 $L LIST CONFLICT 1 * write 217088 221184
   EXPECT c2 $L LIST CONFLICT 1 * write 221184 225280
   EXPECT c2 $L LIST CONFLICT 1 * write 225280 229376
   EXPECT c2 $L LIST CONFLICT 1 * write 229376 233472
   EXPECT c2 $L LIST CONFLICT 1 * write 233472 237568
   EXPECT c2 $L LIST CONFLICT 1 * write 237568 241664
   EXPECT c2 $L LIST CONFLICT 1 * write 241664 245760
   EXPECT c2 $L LIST CONFLICT 1 * write 245760 249856
   EXPECT c2 $L LIST CONFLICT 1 * write 249856 253952
   EXPECT c2 $L LIST CONFLICT 1 * write 253952 258048
   EXPECT c2 $L LIST CONFLICT 1 * write 258048 262144
   EXPECT c2 $L LIST CONFLICT 1 * write 262144 266240
   EXPECT c2 $L LIST CONFLICT 1 * write 266240 270336
   EXPECT c2 $L LIST CONFLICT 1 * write 270336 274432
   EXPECT c2 $L LIST CONFLICT 1 * write 274432 278528
   EXPECT c2 $L LIST CONFLICT 1 * write 278528 282624
   EXPECT c2 $L LIST CONFLICT 1 * write 282624 286720
   EXPECT c2 $L LIST CONFLICT 1 * write 286720 290816
   EXPECT c2 $L LIST CONFLICT 1 * write 290816 294912
   EXPECT c2 $L LIST CONFLICT 1 * write 294912 299008
   EXPECT c2 $L LIST CONFLICT 1 * write 299008 303104
   EXPECT c2 $L LIST CONFLICT 1 * write 303104 307200
   EXPECT c2 $L LIST CONFLICT 1 * write 307200 311296
   EXPECT c2 $L LIST CONFLICT 1 * write 311296 315392
   EXPECT c2 $L LIST CONFLICT 1 * write 315392 319488
   EXPECT c2 $L LIST CONFLICT 1 * write 319488 323584
   EXPECT c2 $L LIST CONFLICT 1 * write 323584 327680
   EXPECT c2 $L LIST CONFLICT 1 * write 327680 331776
   EXPECT c2 $L LIST CONFLICT 1 * write 331776 335872
   EXPECT c2 $L LIST CONFLICT 1 * write 335872 339968
   EXPECT c2 $L LIST CONFLICT 1 * write 339968 344064
   EXPECT c2 $L LIST CONFLICT 1 * write 344064 348160
   EXPECT c2 $L LIST CONFLICT 1 * write 348160 352256
   EXPECT c2 $L LIST CONFLICT 1 * write 352256 356352
   EXPECT c2 $L LIST CONFLICT 1 * write 356352 360448
   EXPECT c2 $L LIST CONFLICT 1 * write 360448 364544
   EXPECT c2 $L LIST CONFLICT 1 * write 364544 368640
   EXPECT c2 $L LIST CONFLICT 1 * write 368640 372736
   EXPECT c2 $L LIST CONFLICT 1 * write 372736 376832
   EXPECT c2 $L LIST CONFLICT 1 * write 376832 380928
   EXPECT c2 $L LIST CONFLICT 1 * write 380928 385024
   EXPECT c2 $L LIST CONFLICT 1 * write 385024 389120
   EXPECT c2 $L LIST CONFLICT 1 * write 389120 393216
   EXPECT c2 $L LIST CONFLICT 1 * write 393216 397312
   EXPECT c2 $L LIST CONFLICT 1 * write 397312 401408
   EXPECT c2 $L LIST CONFLICT 1 * write 401408 405504
   EXPECT c2 $L LIST CONFLICT 1 * write 405504 409600
   EXPECT c2 $L LIST CONFLICT 1 * write 409600 413696
   EXPECT c2 $L LIST CONFLICT 1 * write 413696 417792
   EXPECT c2 $L LIST CONFLICT 1 * write 417792 421888
   EXPECT c2 $L LIST CONFLICT 1 * write 421888 425984
   EXPECT c2 $L LIST CONFLICT 1 * write 425984 430080
   EXPECT c2 $L LIST CONFLICT 1 * write 430080 434176
   EXPECT c2 $L LIST CONFLICT 1 * write 434176 438272
   EXPECT c2 $L LIST CONFLICT 1 * write 438272 442368
   EXPECT c2 $L LIST CONFLICT 1 * write 442368 446464
   EXPECT c2 $L LIST CONFLICT 1 * write 446464 450560
   EXPECT c2 $L LIST CONFLICT 1 * write 450560 454656
   EXPECT c2 $L LIST CONFLICT 1 * write 454656 458752
   EXPECT c2 $L LIST CONFLICT 1 * write 458752 462848
   EXPECT c2 $L LIST CONFLICT 1 * write 462848 466944
   EXPECT c2 $L LIST CONFLICT 1 * write 466944 471040
   EXPECT c2 $L LIST CONFLICT 1 * write 471040 475136
   EXPECT c2 $L LIST CONFLICT 1 * write 475136 479232
   EXPECT c2 $L LIST CONFLICT 1 * write 479232 483328
   EXPECT c2 $L LIST CONFLICT 1 * write 483328 487424
   EXPECT c2 $L LIST CONFLICT 1 * write 487424 491520
   EXPECT c2 $L LIST CONFLICT 1 * write 491520 495616
   EXPECT c2 $L LIST CONFLICT 1 * write 495616 499712
   EXPECT c2 $L LIST CONFLICT 1 * write 499712 503808
   EXPECT c2 $L LIST CONFLICT 1 * write 503808 507904
   EXPECT c2 $L LIST CONFLICT 1 * write 507904 512000
   EXPECT c2 $L LIST CONFLICT 1 * write 512000 516096
   EXPECT c2 $L LIST CONFLICT 1 * write 516096 520192
   EXPECT c2 $L LIST CONFLICT 1 * write 520192 524288
}
EXPECT c2 $L LIST DENIED 1 0 0
GRANTED c1 UNLOCK 1 0 0
AVAILABLE c2 LIST 1 0 0

QUIT
