EXPORT
{
	Export_ID=1;

	Path = "/";

	Pseudo = "/";

	Access_Type = RW;

	Protocols = 4;

	Transports = TCP;

	FSAL {
		Name = RGW;
		User_Id = "testuser";
		Access_Key_Id ="<substitute yours>";
		Secret_Access_Key = "<substitute yours>";
	}
}

MDCACHE {
	# FSAL_RGW needs <PERSON><PERSON>_Chunk to do `readdir` as POSIX style.
	# Because of that, we should never set <PERSON>r_Chunk to `0`.
}

RGW {
	ceph_conf = "/<substitute path to>/ceph.conf";
	# for vstart cluster, name = "client.admin"
	name = "client.rgw.foohost";
	cluster = "ceph";
#	init_args = "-d --debug-rgw=16";
}
