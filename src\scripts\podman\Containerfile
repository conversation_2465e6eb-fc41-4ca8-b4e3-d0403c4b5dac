ARG IMAGE

FROM $IMAGE

ARG USER_ID
ARG GROUP_ID

USER root

COPY ["install-packages.sh", "/tmp"]

RUN "/tmp/install-packages.sh"

RUN { userdel ubuntu >/dev/null 2>&1 || true; } && \
    { groupdel ubuntu >/dev/null 2>&1 || true; } && \
    groupadd -g "$GROUP_ID" user && \
    useradd -m -u "$USER_ID" -g "$GROUP_ID" -s /bin/bash user && \
    mkdir -p /etc/sudoers.d && \
    echo "user ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/container

USER user
