#
# Fichier de configuration de test 
#
# $Header: /cea/home/<USER>/cvs/SHERPA/BaseCvs/GANESHA/src/MainNFSD/maketest.conf,v 1.2 2004/10/22 09:24:32 deniel Exp $
# 
# $Log: maketest.conf,v $
# Revision 1.2  2004/10/22 09:24:32  deniel
# No more dynamic libraries compiled
#
# Revision 1.1  2004/10/04 14:05:19  deniel
# Dispatcher thread presque ok
#
# Revision 1.1  2004/09/30 14:08:42  deniel
# Ajout des log en anglais (a partir des logs aglae)
#
#
#

Test Test_libloghash_Static
{
   Product = Static version of the log library
   Command = ksh ../scripts/run_test_liblog.ksh
   Comment = Check for correctness of the log

        Success TestOk
        {
          STDOUT =~ /Test reussi : tous les tests sont passes avec succes/m
            AND
          STATUS == 0 
        }

        Failure FoundBadValue
        {
           STATUS != 0
             AND
           STDOUT =~ /Test ECHOUE : la valeur lue est incorrecte/m
        }

	Failure BadHashTableInit
	{
	   STATUS != 0 
	     AND
           STDOUT =~ /Test ECHOUE : Mauvaise init/m 
	}

	Failure BadHashDelete
	{
	   STATUS != 0 
	     AND
           STDOUT =~ /Test ECHOUE : effacement incorrect/m
	}

	Failure BadStatistique
	{
	   STATUS != 0 
	     AND
           STDOUT =~ /Test ECHOUE : statistiques incorrectes/m
	}

	Failure KeyIncoherence
	{
	   STATUS != 0 
	     AND
           STDOUT =~ /Test ECHOUE : La clef n'est pas trouvee/m 
	}
	
	Failure RedondantKey
	{
	   STATUS != 0 
	     AND
           STDOUT =~ /Test ECHOUE : Clef redondante/m
	}
}




