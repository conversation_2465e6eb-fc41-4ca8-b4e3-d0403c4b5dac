
###################################################

EXPORT
{
  # Export Id (mandatory)
  Export_Id = 77 ;
  Path = "/";
  FSAL {
    name = KVSFS;
    kvsns_config = /etc/kvsns.d/kvsns.ini;
  }
  Pseudo = /kvsfs;
  Protocols=  NFSV3, 4, 9p;
  SecType = sys;
  MaxRead = 32768;
  MaxWrite = 32768;
  Filesystem_id = 192.168;
  client {
        clients = *;
	Squash=no_root_squash;
        access_type=RW;
        protocols = 3, 4, 9p;
  }

}


