# Implementation of same file concurrent locks with varied ranges
#
# Take 128 locks on a file with medium ranges and medium gaps
# medium = 1 * size.MiB
# Check to see that those locks exists
# Release the locks
# Check to see that those locks don’t exists

CLIENTS c1 c2

OK c1 OPEN 1 rw create OFD testFile
OK c2 OPEN 1 rw OFD testFile

GRANTED c1 LOCK 1 read 0 1048576
GRANTED c1 LOCK 1 read 2097152 3145728
GRANTED c1 LOCK 1 read 4194304 5242880
GRANTED c1 LOCK 1 read 6291456 7340032
GRANTED c1 LOCK 1 read 8388608 9437184
GRANTED c1 LOCK 1 read 10485760 11534336
GRANTED c1 LOCK 1 read 12582912 13631488
GRANTED c1 LOCK 1 read 14680064 15728640
GRANTED c1 LOCK 1 read 16777216 17825792
GRANTED c1 LOCK 1 read 18874368 19922944
GRANTED c1 LOCK 1 read 20971520 22020096
GRANTED c1 LOCK 1 read 23068672 24117248
GRANTED c1 LOCK 1 read 25165824 26214400
GRANTED c1 LOCK 1 read 27262976 28311552
GRANTED c1 LOCK 1 read 29360128 30408704
GRANTED c1 LOCK 1 read 31457280 32505856
GRANTED c1 LOCK 1 read 33554432 34603008
GRANTED c1 LOCK 1 read 35651584 36700160
GRANTED c1 LOCK 1 read 37748736 38797312
GRANTED c1 LOCK 1 read 39845888 40894464
GRANTED c1 LOCK 1 read 41943040 42991616
GRANTED c1 LOCK 1 read 44040192 45088768
GRANTED c1 LOCK 1 read 46137344 47185920
GRANTED c1 LOCK 1 read 48234496 49283072
GRANTED c1 LOCK 1 read 50331648 51380224
GRANTED c1 LOCK 1 read 52428800 53477376
GRANTED c1 LOCK 1 read 54525952 55574528
GRANTED c1 LOCK 1 read 56623104 57671680
GRANTED c1 LOCK 1 read 58720256 59768832
GRANTED c1 LOCK 1 read 60817408 61865984
GRANTED c1 LOCK 1 read 62914560 63963136
GRANTED c1 LOCK 1 read 65011712 66060288
GRANTED c1 LOCK 1 read 67108864 68157440
GRANTED c1 LOCK 1 read 69206016 70254592
GRANTED c1 LOCK 1 read 71303168 72351744
GRANTED c1 LOCK 1 read 73400320 74448896
GRANTED c1 LOCK 1 read 75497472 76546048
GRANTED c1 LOCK 1 read 77594624 78643200
GRANTED c1 LOCK 1 read 79691776 80740352
GRANTED c1 LOCK 1 read 81788928 82837504
GRANTED c1 LOCK 1 read 83886080 84934656
GRANTED c1 LOCK 1 read 85983232 87031808
GRANTED c1 LOCK 1 read 88080384 89128960
GRANTED c1 LOCK 1 read 90177536 91226112
GRANTED c1 LOCK 1 read 92274688 93323264
GRANTED c1 LOCK 1 read 94371840 95420416
GRANTED c1 LOCK 1 read 96468992 97517568
GRANTED c1 LOCK 1 read 98566144 99614720
GRANTED c1 LOCK 1 read 100663296 101711872
GRANTED c1 LOCK 1 read 102760448 103809024
GRANTED c1 LOCK 1 read 104857600 105906176
GRANTED c1 LOCK 1 read 106954752 108003328
GRANTED c1 LOCK 1 read 109051904 110100480
GRANTED c1 LOCK 1 read 111149056 112197632
GRANTED c1 LOCK 1 read 113246208 114294784
GRANTED c1 LOCK 1 read 115343360 116391936
GRANTED c1 LOCK 1 read 117440512 118489088
GRANTED c1 LOCK 1 read 119537664 120586240
GRANTED c1 LOCK 1 read 121634816 122683392
GRANTED c1 LOCK 1 read 123731968 124780544
GRANTED c1 LOCK 1 read 125829120 126877696
GRANTED c1 LOCK 1 read 127926272 128974848
GRANTED c1 LOCK 1 read 130023424 131072000
GRANTED c1 LOCK 1 read 132120576 133169152
GRANTED c1 LOCK 1 read 134217728 135266304
GRANTED c1 LOCK 1 read 136314880 137363456
GRANTED c1 LOCK 1 read 138412032 139460608
GRANTED c1 LOCK 1 read 140509184 141557760
GRANTED c1 LOCK 1 read 142606336 143654912
GRANTED c1 LOCK 1 read 144703488 145752064
GRANTED c1 LOCK 1 read 146800640 147849216
GRANTED c1 LOCK 1 read 148897792 149946368
GRANTED c1 LOCK 1 read 150994944 152043520
GRANTED c1 LOCK 1 read 153092096 154140672
GRANTED c1 LOCK 1 read 155189248 156237824
GRANTED c1 LOCK 1 read 157286400 158334976
GRANTED c1 LOCK 1 read 159383552 160432128
GRANTED c1 LOCK 1 read 161480704 162529280
GRANTED c1 LOCK 1 read 163577856 164626432
GRANTED c1 LOCK 1 read 165675008 166723584
GRANTED c1 LOCK 1 read 167772160 168820736
GRANTED c1 LOCK 1 read 169869312 170917888
GRANTED c1 LOCK 1 read 171966464 173015040
GRANTED c1 LOCK 1 read 174063616 175112192
GRANTED c1 LOCK 1 read 176160768 177209344
GRANTED c1 LOCK 1 read 178257920 179306496
GRANTED c1 LOCK 1 read 180355072 181403648
GRANTED c1 LOCK 1 read 182452224 183500800
GRANTED c1 LOCK 1 read 184549376 185597952
GRANTED c1 LOCK 1 read 186646528 187695104
GRANTED c1 LOCK 1 read 188743680 189792256
GRANTED c1 LOCK 1 read 190840832 191889408
GRANTED c1 LOCK 1 read 192937984 193986560
GRANTED c1 LOCK 1 read 195035136 196083712
GRANTED c1 LOCK 1 read 197132288 198180864
GRANTED c1 LOCK 1 read 199229440 200278016
GRANTED c1 LOCK 1 read 201326592 202375168
GRANTED c1 LOCK 1 read 203423744 204472320
GRANTED c1 LOCK 1 read 205520896 206569472
GRANTED c1 LOCK 1 read 207618048 208666624
GRANTED c1 LOCK 1 read 209715200 210763776
GRANTED c1 LOCK 1 read 211812352 212860928
GRANTED c1 LOCK 1 read 213909504 214958080
GRANTED c1 LOCK 1 read 216006656 217055232
GRANTED c1 LOCK 1 read 218103808 219152384
GRANTED c1 LOCK 1 read 220200960 221249536
GRANTED c1 LOCK 1 read 222298112 223346688
GRANTED c1 LOCK 1 read 224395264 225443840
GRANTED c1 LOCK 1 read 226492416 227540992
GRANTED c1 LOCK 1 read 228589568 229638144
GRANTED c1 LOCK 1 read 230686720 231735296
GRANTED c1 LOCK 1 read 232783872 233832448
GRANTED c1 LOCK 1 read 234881024 235929600
GRANTED c1 LOCK 1 read 236978176 238026752
GRANTED c1 LOCK 1 read 239075328 240123904
GRANTED c1 LOCK 1 read 241172480 242221056
GRANTED c1 LOCK 1 read 243269632 244318208
GRANTED c1 LOCK 1 read 245366784 246415360
GRANTED c1 LOCK 1 read 247463936 248512512
GRANTED c1 LOCK 1 read 249561088 250609664
GRANTED c1 LOCK 1 read 251658240 252706816
GRANTED c1 LOCK 1 read 253755392 254803968
GRANTED c1 LOCK 1 read 255852544 256901120
GRANTED c1 LOCK 1 read 257949696 258998272
GRANTED c1 LOCK 1 read 260046848 261095424
GRANTED c1 LOCK 1 read 262144000 263192576
GRANTED c1 LOCK 1 read 264241152 265289728
GRANTED c1 LOCK 1 read 266338304 267386880

c2 $L LIST 1 0 0
{
  EXPECT c2 $L LIST CONFLICT 1 * read 0 1048576
  EXPECT c2 $L LIST CONFLICT 1 * read 2097152 3145728
  EXPECT c2 $L LIST CONFLICT 1 * read 4194304 5242880
  EXPECT c2 $L LIST CONFLICT 1 * read 6291456 7340032
  EXPECT c2 $L LIST CONFLICT 1 * read 8388608 9437184
  EXPECT c2 $L LIST CONFLICT 1 * read 10485760 11534336
  EXPECT c2 $L LIST CONFLICT 1 * read 12582912 13631488
  EXPECT c2 $L LIST CONFLICT 1 * read 14680064 15728640
  EXPECT c2 $L LIST CONFLICT 1 * read 16777216 17825792
  EXPECT c2 $L LIST CONFLICT 1 * read 18874368 19922944
  EXPECT c2 $L LIST CONFLICT 1 * read 20971520 22020096
  EXPECT c2 $L LIST CONFLICT 1 * read 23068672 24117248
  EXPECT c2 $L LIST CONFLICT 1 * read 25165824 26214400
  EXPECT c2 $L LIST CONFLICT 1 * read 27262976 28311552
  EXPECT c2 $L LIST CONFLICT 1 * read 29360128 30408704
  EXPECT c2 $L LIST CONFLICT 1 * read 31457280 32505856
  EXPECT c2 $L LIST CONFLICT 1 * read 33554432 34603008
  EXPECT c2 $L LIST CONFLICT 1 * read 35651584 36700160
  EXPECT c2 $L LIST CONFLICT 1 * read 37748736 38797312
  EXPECT c2 $L LIST CONFLICT 1 * read 39845888 40894464
  EXPECT c2 $L LIST CONFLICT 1 * read 41943040 42991616
  EXPECT c2 $L LIST CONFLICT 1 * read 44040192 45088768
  EXPECT c2 $L LIST CONFLICT 1 * read 46137344 47185920
  EXPECT c2 $L LIST CONFLICT 1 * read 48234496 49283072
  EXPECT c2 $L LIST CONFLICT 1 * read 50331648 51380224
  EXPECT c2 $L LIST CONFLICT 1 * read 52428800 53477376
  EXPECT c2 $L LIST CONFLICT 1 * read 54525952 55574528
  EXPECT c2 $L LIST CONFLICT 1 * read 56623104 57671680
  EXPECT c2 $L LIST CONFLICT 1 * read 58720256 59768832
  EXPECT c2 $L LIST CONFLICT 1 * read 60817408 61865984
  EXPECT c2 $L LIST CONFLICT 1 * read 62914560 63963136
  EXPECT c2 $L LIST CONFLICT 1 * read 65011712 66060288
  EXPECT c2 $L LIST CONFLICT 1 * read 67108864 68157440
  EXPECT c2 $L LIST CONFLICT 1 * read 69206016 70254592
  EXPECT c2 $L LIST CONFLICT 1 * read 71303168 72351744
  EXPECT c2 $L LIST CONFLICT 1 * read 73400320 74448896
  EXPECT c2 $L LIST CONFLICT 1 * read 75497472 76546048
  EXPECT c2 $L LIST CONFLICT 1 * read 77594624 78643200
  EXPECT c2 $L LIST CONFLICT 1 * read 79691776 80740352
  EXPECT c2 $L LIST CONFLICT 1 * read 81788928 82837504
  EXPECT c2 $L LIST CONFLICT 1 * read 83886080 84934656
  EXPECT c2 $L LIST CONFLICT 1 * read 85983232 87031808
  EXPECT c2 $L LIST CONFLICT 1 * read 88080384 89128960
  EXPECT c2 $L LIST CONFLICT 1 * read 90177536 91226112
  EXPECT c2 $L LIST CONFLICT 1 * read 92274688 93323264
  EXPECT c2 $L LIST CONFLICT 1 * read 94371840 95420416
  EXPECT c2 $L LIST CONFLICT 1 * read 96468992 97517568
  EXPECT c2 $L LIST CONFLICT 1 * read 98566144 99614720
  EXPECT c2 $L LIST CONFLICT 1 * read 100663296 101711872
  EXPECT c2 $L LIST CONFLICT 1 * read 102760448 103809024
  EXPECT c2 $L LIST CONFLICT 1 * read 104857600 105906176
  EXPECT c2 $L LIST CONFLICT 1 * read 106954752 108003328
  EXPECT c2 $L LIST CONFLICT 1 * read 109051904 110100480
  EXPECT c2 $L LIST CONFLICT 1 * read 111149056 112197632
  EXPECT c2 $L LIST CONFLICT 1 * read 113246208 114294784
  EXPECT c2 $L LIST CONFLICT 1 * read 115343360 116391936
  EXPECT c2 $L LIST CONFLICT 1 * read 117440512 118489088
  EXPECT c2 $L LIST CONFLICT 1 * read 119537664 120586240
  EXPECT c2 $L LIST CONFLICT 1 * read 121634816 122683392
  EXPECT c2 $L LIST CONFLICT 1 * read 123731968 124780544
  EXPECT c2 $L LIST CONFLICT 1 * read 125829120 126877696
  EXPECT c2 $L LIST CONFLICT 1 * read 127926272 128974848
  EXPECT c2 $L LIST CONFLICT 1 * read 130023424 131072000
  EXPECT c2 $L LIST CONFLICT 1 * read 132120576 133169152
  EXPECT c2 $L LIST CONFLICT 1 * read 134217728 135266304
  EXPECT c2 $L LIST CONFLICT 1 * read 136314880 137363456
  EXPECT c2 $L LIST CONFLICT 1 * read 138412032 139460608
  EXPECT c2 $L LIST CONFLICT 1 * read 140509184 141557760
  EXPECT c2 $L LIST CONFLICT 1 * read 142606336 143654912
  EXPECT c2 $L LIST CONFLICT 1 * read 144703488 145752064
  EXPECT c2 $L LIST CONFLICT 1 * read 146800640 147849216
  EXPECT c2 $L LIST CONFLICT 1 * read 148897792 149946368
  EXPECT c2 $L LIST CONFLICT 1 * read 150994944 152043520
  EXPECT c2 $L LIST CONFLICT 1 * read 153092096 154140672
  EXPECT c2 $L LIST CONFLICT 1 * read 155189248 156237824
  EXPECT c2 $L LIST CONFLICT 1 * read 157286400 158334976
  EXPECT c2 $L LIST CONFLICT 1 * read 159383552 160432128
  EXPECT c2 $L LIST CONFLICT 1 * read 161480704 162529280
  EXPECT c2 $L LIST CONFLICT 1 * read 163577856 164626432
  EXPECT c2 $L LIST CONFLICT 1 * read 165675008 166723584
  EXPECT c2 $L LIST CONFLICT 1 * read 167772160 168820736
  EXPECT c2 $L LIST CONFLICT 1 * read 169869312 170917888
  EXPECT c2 $L LIST CONFLICT 1 * read 171966464 173015040
  EXPECT c2 $L LIST CONFLICT 1 * read 174063616 175112192
  EXPECT c2 $L LIST CONFLICT 1 * read 176160768 177209344
  EXPECT c2 $L LIST CONFLICT 1 * read 178257920 179306496
  EXPECT c2 $L LIST CONFLICT 1 * read 180355072 181403648
  EXPECT c2 $L LIST CONFLICT 1 * read 182452224 183500800
  EXPECT c2 $L LIST CONFLICT 1 * read 184549376 185597952
  EXPECT c2 $L LIST CONFLICT 1 * read 186646528 187695104
  EXPECT c2 $L LIST CONFLICT 1 * read 188743680 189792256
  EXPECT c2 $L LIST CONFLICT 1 * read 190840832 191889408
  EXPECT c2 $L LIST CONFLICT 1 * read 192937984 193986560
  EXPECT c2 $L LIST CONFLICT 1 * read 195035136 196083712
  EXPECT c2 $L LIST CONFLICT 1 * read 197132288 198180864
  EXPECT c2 $L LIST CONFLICT 1 * read 199229440 200278016
  EXPECT c2 $L LIST CONFLICT 1 * read 201326592 202375168
  EXPECT c2 $L LIST CONFLICT 1 * read 203423744 204472320
  EXPECT c2 $L LIST CONFLICT 1 * read 205520896 206569472
  EXPECT c2 $L LIST CONFLICT 1 * read 207618048 208666624
  EXPECT c2 $L LIST CONFLICT 1 * read 209715200 210763776
  EXPECT c2 $L LIST CONFLICT 1 * read 211812352 212860928
  EXPECT c2 $L LIST CONFLICT 1 * read 213909504 214958080
  EXPECT c2 $L LIST CONFLICT 1 * read 216006656 217055232
  EXPECT c2 $L LIST CONFLICT 1 * read 218103808 219152384
  EXPECT c2 $L LIST CONFLICT 1 * read 220200960 221249536
  EXPECT c2 $L LIST CONFLICT 1 * read 222298112 223346688
  EXPECT c2 $L LIST CONFLICT 1 * read 224395264 225443840
  EXPECT c2 $L LIST CONFLICT 1 * read 226492416 227540992
  EXPECT c2 $L LIST CONFLICT 1 * read 228589568 229638144
  EXPECT c2 $L LIST CONFLICT 1 * read 230686720 231735296
  EXPECT c2 $L LIST CONFLICT 1 * read 232783872 233832448
  EXPECT c2 $L LIST CONFLICT 1 * read 234881024 235929600
  EXPECT c2 $L LIST CONFLICT 1 * read 236978176 238026752
  EXPECT c2 $L LIST CONFLICT 1 * read 239075328 240123904
  EXPECT c2 $L LIST CONFLICT 1 * read 241172480 242221056
  EXPECT c2 $L LIST CONFLICT 1 * read 243269632 244318208
  EXPECT c2 $L LIST CONFLICT 1 * read 245366784 246415360
  EXPECT c2 $L LIST CONFLICT 1 * read 247463936 248512512
  EXPECT c2 $L LIST CONFLICT 1 * read 249561088 250609664
  EXPECT c2 $L LIST CONFLICT 1 * read 251658240 252706816
  EXPECT c2 $L LIST CONFLICT 1 * read 253755392 254803968
  EXPECT c2 $L LIST CONFLICT 1 * read 255852544 256901120
  EXPECT c2 $L LIST CONFLICT 1 * read 257949696 258998272
  EXPECT c2 $L LIST CONFLICT 1 * read 260046848 261095424
  EXPECT c2 $L LIST CONFLICT 1 * read 262144000 263192576
  EXPECT c2 $L LIST CONFLICT 1 * read 264241152 265289728
  EXPECT c2 $L LIST CONFLICT 1 * read 266338304 267386880
}
EXPECT c2 $L LIST DENIED 1 0 0
GRANTED c1 UNLOCK 1 0 0
AVAILABLE c2 LIST 1 0 0

GRANTED c1 LOCK 1 write 0 1048576
GRANTED c1 LOCK 1 write 2097152 3145728
GRANTED c1 LOCK 1 write 4194304 5242880
GRANTED c1 LOCK 1 write 6291456 7340032
GRANTED c1 LOCK 1 write 8388608 9437184
GRANTED c1 LOCK 1 write 10485760 11534336
GRANTED c1 LOCK 1 write 12582912 13631488
GRANTED c1 LOCK 1 write 14680064 15728640
GRANTED c1 LOCK 1 write 16777216 17825792
GRANTED c1 LOCK 1 write 18874368 19922944
GRANTED c1 LOCK 1 write 20971520 22020096
GRANTED c1 LOCK 1 write 23068672 24117248
GRANTED c1 LOCK 1 write 25165824 26214400
GRANTED c1 LOCK 1 write 27262976 28311552
GRANTED c1 LOCK 1 write 29360128 30408704
GRANTED c1 LOCK 1 write 31457280 32505856
GRANTED c1 LOCK 1 write 33554432 34603008
GRANTED c1 LOCK 1 write 35651584 36700160
GRANTED c1 LOCK 1 write 37748736 38797312
GRANTED c1 LOCK 1 write 39845888 40894464
GRANTED c1 LOCK 1 write 41943040 42991616
GRANTED c1 LOCK 1 write 44040192 45088768
GRANTED c1 LOCK 1 write 46137344 47185920
GRANTED c1 LOCK 1 write 48234496 49283072
GRANTED c1 LOCK 1 write 50331648 51380224
GRANTED c1 LOCK 1 write 52428800 53477376
GRANTED c1 LOCK 1 write 54525952 55574528
GRANTED c1 LOCK 1 write 56623104 57671680
GRANTED c1 LOCK 1 write 58720256 59768832
GRANTED c1 LOCK 1 write 60817408 61865984
GRANTED c1 LOCK 1 write 62914560 63963136
GRANTED c1 LOCK 1 write 65011712 66060288
GRANTED c1 LOCK 1 write 67108864 68157440
GRANTED c1 LOCK 1 write 69206016 70254592
GRANTED c1 LOCK 1 write 71303168 72351744
GRANTED c1 LOCK 1 write 73400320 74448896
GRANTED c1 LOCK 1 write 75497472 76546048
GRANTED c1 LOCK 1 write 77594624 78643200
GRANTED c1 LOCK 1 write 79691776 80740352
GRANTED c1 LOCK 1 write 81788928 82837504
GRANTED c1 LOCK 1 write 83886080 84934656
GRANTED c1 LOCK 1 write 85983232 87031808
GRANTED c1 LOCK 1 write 88080384 89128960
GRANTED c1 LOCK 1 write 90177536 91226112
GRANTED c1 LOCK 1 write 92274688 93323264
GRANTED c1 LOCK 1 write 94371840 95420416
GRANTED c1 LOCK 1 write 96468992 97517568
GRANTED c1 LOCK 1 write 98566144 99614720
GRANTED c1 LOCK 1 write 100663296 101711872
GRANTED c1 LOCK 1 write 102760448 103809024
GRANTED c1 LOCK 1 write 104857600 105906176
GRANTED c1 LOCK 1 write 106954752 108003328
GRANTED c1 LOCK 1 write 109051904 110100480
GRANTED c1 LOCK 1 write 111149056 112197632
GRANTED c1 LOCK 1 write 113246208 114294784
GRANTED c1 LOCK 1 write 115343360 116391936
GRANTED c1 LOCK 1 write 117440512 118489088
GRANTED c1 LOCK 1 write 119537664 120586240
GRANTED c1 LOCK 1 write 121634816 122683392
GRANTED c1 LOCK 1 write 123731968 124780544
GRANTED c1 LOCK 1 write 125829120 126877696
GRANTED c1 LOCK 1 write 127926272 128974848
GRANTED c1 LOCK 1 write 130023424 131072000
GRANTED c1 LOCK 1 write 132120576 133169152
GRANTED c1 LOCK 1 write 134217728 135266304
GRANTED c1 LOCK 1 write 136314880 137363456
GRANTED c1 LOCK 1 write 138412032 139460608
GRANTED c1 LOCK 1 write 140509184 141557760
GRANTED c1 LOCK 1 write 142606336 143654912
GRANTED c1 LOCK 1 write 144703488 145752064
GRANTED c1 LOCK 1 write 146800640 147849216
GRANTED c1 LOCK 1 write 148897792 149946368
GRANTED c1 LOCK 1 write 150994944 152043520
GRANTED c1 LOCK 1 write 153092096 154140672
GRANTED c1 LOCK 1 write 155189248 156237824
GRANTED c1 LOCK 1 write 157286400 158334976
GRANTED c1 LOCK 1 write 159383552 160432128
GRANTED c1 LOCK 1 write 161480704 162529280
GRANTED c1 LOCK 1 write 163577856 164626432
GRANTED c1 LOCK 1 write 165675008 166723584
GRANTED c1 LOCK 1 write 167772160 168820736
GRANTED c1 LOCK 1 write 169869312 170917888
GRANTED c1 LOCK 1 write 171966464 173015040
GRANTED c1 LOCK 1 write 174063616 175112192
GRANTED c1 LOCK 1 write 176160768 177209344
GRANTED c1 LOCK 1 write 178257920 179306496
GRANTED c1 LOCK 1 write 180355072 181403648
GRANTED c1 LOCK 1 write 182452224 183500800
GRANTED c1 LOCK 1 write 184549376 185597952
GRANTED c1 LOCK 1 write 186646528 187695104
GRANTED c1 LOCK 1 write 188743680 189792256
GRANTED c1 LOCK 1 write 190840832 191889408
GRANTED c1 LOCK 1 write 192937984 193986560
GRANTED c1 LOCK 1 write 195035136 196083712
GRANTED c1 LOCK 1 write 197132288 198180864
GRANTED c1 LOCK 1 write 199229440 200278016
GRANTED c1 LOCK 1 write 201326592 202375168
GRANTED c1 LOCK 1 write 203423744 204472320
GRANTED c1 LOCK 1 write 205520896 206569472
GRANTED c1 LOCK 1 write 207618048 208666624
GRANTED c1 LOCK 1 write 209715200 210763776
GRANTED c1 LOCK 1 write 211812352 212860928
GRANTED c1 LOCK 1 write 213909504 214958080
GRANTED c1 LOCK 1 write 216006656 217055232
GRANTED c1 LOCK 1 write 218103808 219152384
GRANTED c1 LOCK 1 write 220200960 221249536
GRANTED c1 LOCK 1 write 222298112 223346688
GRANTED c1 LOCK 1 write 224395264 225443840
GRANTED c1 LOCK 1 write 226492416 227540992
GRANTED c1 LOCK 1 write 228589568 229638144
GRANTED c1 LOCK 1 write 230686720 231735296
GRANTED c1 LOCK 1 write 232783872 233832448
GRANTED c1 LOCK 1 write 234881024 235929600
GRANTED c1 LOCK 1 write 236978176 238026752
GRANTED c1 LOCK 1 write 239075328 240123904
GRANTED c1 LOCK 1 write 241172480 242221056
GRANTED c1 LOCK 1 write 243269632 244318208
GRANTED c1 LOCK 1 write 245366784 246415360
GRANTED c1 LOCK 1 write 247463936 248512512
GRANTED c1 LOCK 1 write 249561088 250609664
GRANTED c1 LOCK 1 write 251658240 252706816
GRANTED c1 LOCK 1 write 253755392 254803968
GRANTED c1 LOCK 1 write 255852544 256901120
GRANTED c1 LOCK 1 write 257949696 258998272
GRANTED c1 LOCK 1 write 260046848 261095424
GRANTED c1 LOCK 1 write 262144000 263192576
GRANTED c1 LOCK 1 write 264241152 265289728
GRANTED c1 LOCK 1 write 266338304 267386880

c2 $L LIST 1 0 0
{
  EXPECT c2 $L LIST CONFLICT 1 * write 0 1048576
  EXPECT c2 $L LIST CONFLICT 1 * write 2097152 3145728
  EXPECT c2 $L LIST CONFLICT 1 * write 4194304 5242880
  EXPECT c2 $L LIST CONFLICT 1 * write 6291456 7340032
  EXPECT c2 $L LIST CONFLICT 1 * write 8388608 9437184
  EXPECT c2 $L LIST CONFLICT 1 * write 10485760 11534336
  EXPECT c2 $L LIST CONFLICT 1 * write 12582912 13631488
  EXPECT c2 $L LIST CONFLICT 1 * write 14680064 15728640
  EXPECT c2 $L LIST CONFLICT 1 * write 16777216 17825792
  EXPECT c2 $L LIST CONFLICT 1 * write 18874368 19922944
  EXPECT c2 $L LIST CONFLICT 1 * write 20971520 22020096
  EXPECT c2 $L LIST CONFLICT 1 * write 23068672 24117248
  EXPECT c2 $L LIST CONFLICT 1 * write 25165824 26214400
  EXPECT c2 $L LIST CONFLICT 1 * write 27262976 28311552
  EXPECT c2 $L LIST CONFLICT 1 * write 29360128 30408704
  EXPECT c2 $L LIST CONFLICT 1 * write 31457280 32505856
  EXPECT c2 $L LIST CONFLICT 1 * write 33554432 34603008
  EXPECT c2 $L LIST CONFLICT 1 * write 35651584 36700160
  EXPECT c2 $L LIST CONFLICT 1 * write 37748736 38797312
  EXPECT c2 $L LIST CONFLICT 1 * write 39845888 40894464
  EXPECT c2 $L LIST CONFLICT 1 * write 41943040 42991616
  EXPECT c2 $L LIST CONFLICT 1 * write 44040192 45088768
  EXPECT c2 $L LIST CONFLICT 1 * write 46137344 47185920
  EXPECT c2 $L LIST CONFLICT 1 * write 48234496 49283072
  EXPECT c2 $L LIST CONFLICT 1 * write 50331648 51380224
  EXPECT c2 $L LIST CONFLICT 1 * write 52428800 53477376
  EXPECT c2 $L LIST CONFLICT 1 * write 54525952 55574528
  EXPECT c2 $L LIST CONFLICT 1 * write 56623104 57671680
  EXPECT c2 $L LIST CONFLICT 1 * write 58720256 59768832
  EXPECT c2 $L LIST CONFLICT 1 * write 60817408 61865984
  EXPECT c2 $L LIST CONFLICT 1 * write 62914560 63963136
  EXPECT c2 $L LIST CONFLICT 1 * write 65011712 66060288
  EXPECT c2 $L LIST CONFLICT 1 * write 67108864 68157440
  EXPECT c2 $L LIST CONFLICT 1 * write 69206016 70254592
  EXPECT c2 $L LIST CONFLICT 1 * write 71303168 72351744
  EXPECT c2 $L LIST CONFLICT 1 * write 73400320 74448896
  EXPECT c2 $L LIST CONFLICT 1 * write 75497472 76546048
  EXPECT c2 $L LIST CONFLICT 1 * write 77594624 78643200
  EXPECT c2 $L LIST CONFLICT 1 * write 79691776 80740352
  EXPECT c2 $L LIST CONFLICT 1 * write 81788928 82837504
  EXPECT c2 $L LIST CONFLICT 1 * write 83886080 84934656
  EXPECT c2 $L LIST CONFLICT 1 * write 85983232 87031808
  EXPECT c2 $L LIST CONFLICT 1 * write 88080384 89128960
  EXPECT c2 $L LIST CONFLICT 1 * write 90177536 91226112
  EXPECT c2 $L LIST CONFLICT 1 * write 92274688 93323264
  EXPECT c2 $L LIST CONFLICT 1 * write 94371840 95420416
  EXPECT c2 $L LIST CONFLICT 1 * write 96468992 97517568
  EXPECT c2 $L LIST CONFLICT 1 * write 98566144 99614720
  EXPECT c2 $L LIST CONFLICT 1 * write 100663296 101711872
  EXPECT c2 $L LIST CONFLICT 1 * write 102760448 103809024
  EXPECT c2 $L LIST CONFLICT 1 * write 104857600 105906176
  EXPECT c2 $L LIST CONFLICT 1 * write 106954752 108003328
  EXPECT c2 $L LIST CONFLICT 1 * write 109051904 110100480
  EXPECT c2 $L LIST CONFLICT 1 * write 111149056 112197632
  EXPECT c2 $L LIST CONFLICT 1 * write 113246208 114294784
  EXPECT c2 $L LIST CONFLICT 1 * write 115343360 116391936
  EXPECT c2 $L LIST CONFLICT 1 * write 117440512 118489088
  EXPECT c2 $L LIST CONFLICT 1 * write 119537664 120586240
  EXPECT c2 $L LIST CONFLICT 1 * write 121634816 122683392
  EXPECT c2 $L LIST CONFLICT 1 * write 123731968 124780544
  EXPECT c2 $L LIST CONFLICT 1 * write 125829120 126877696
  EXPECT c2 $L LIST CONFLICT 1 * write 127926272 128974848
  EXPECT c2 $L LIST CONFLICT 1 * write 130023424 131072000
  EXPECT c2 $L LIST CONFLICT 1 * write 132120576 133169152
  EXPECT c2 $L LIST CONFLICT 1 * write 134217728 135266304
  EXPECT c2 $L LIST CONFLICT 1 * write 136314880 137363456
  EXPECT c2 $L LIST CONFLICT 1 * write 138412032 139460608
  EXPECT c2 $L LIST CONFLICT 1 * write 140509184 141557760
  EXPECT c2 $L LIST CONFLICT 1 * write 142606336 143654912
  EXPECT c2 $L LIST CONFLICT 1 * write 144703488 145752064
  EXPECT c2 $L LIST CONFLICT 1 * write 146800640 147849216
  EXPECT c2 $L LIST CONFLICT 1 * write 148897792 149946368
  EXPECT c2 $L LIST CONFLICT 1 * write 150994944 152043520
  EXPECT c2 $L LIST CONFLICT 1 * write 153092096 154140672
  EXPECT c2 $L LIST CONFLICT 1 * write 155189248 156237824
  EXPECT c2 $L LIST CONFLICT 1 * write 157286400 158334976
  EXPECT c2 $L LIST CONFLICT 1 * write 159383552 160432128
  EXPECT c2 $L LIST CONFLICT 1 * write 161480704 162529280
  EXPECT c2 $L LIST CONFLICT 1 * write 163577856 164626432
  EXPECT c2 $L LIST CONFLICT 1 * write 165675008 166723584
  EXPECT c2 $L LIST CONFLICT 1 * write 167772160 168820736
  EXPECT c2 $L LIST CONFLICT 1 * write 169869312 170917888
  EXPECT c2 $L LIST CONFLICT 1 * write 171966464 173015040
  EXPECT c2 $L LIST CONFLICT 1 * write 174063616 175112192
  EXPECT c2 $L LIST CONFLICT 1 * write 176160768 177209344
  EXPECT c2 $L LIST CONFLICT 1 * write 178257920 179306496
  EXPECT c2 $L LIST CONFLICT 1 * write 180355072 181403648
  EXPECT c2 $L LIST CONFLICT 1 * write 182452224 183500800
  EXPECT c2 $L LIST CONFLICT 1 * write 184549376 185597952
  EXPECT c2 $L LIST CONFLICT 1 * write 186646528 187695104
  EXPECT c2 $L LIST CONFLICT 1 * write 188743680 189792256
  EXPECT c2 $L LIST CONFLICT 1 * write 190840832 191889408
  EXPECT c2 $L LIST CONFLICT 1 * write 192937984 193986560
  EXPECT c2 $L LIST CONFLICT 1 * write 195035136 196083712
  EXPECT c2 $L LIST CONFLICT 1 * write 197132288 198180864
  EXPECT c2 $L LIST CONFLICT 1 * write 199229440 200278016
  EXPECT c2 $L LIST CONFLICT 1 * write 201326592 202375168
  EXPECT c2 $L LIST CONFLICT 1 * write 203423744 204472320
  EXPECT c2 $L LIST CONFLICT 1 * write 205520896 206569472
  EXPECT c2 $L LIST CONFLICT 1 * write 207618048 208666624
  EXPECT c2 $L LIST CONFLICT 1 * write 209715200 210763776
  EXPECT c2 $L LIST CONFLICT 1 * write 211812352 212860928
  EXPECT c2 $L LIST CONFLICT 1 * write 213909504 214958080
  EXPECT c2 $L LIST CONFLICT 1 * write 216006656 217055232
  EXPECT c2 $L LIST CONFLICT 1 * write 218103808 219152384
  EXPECT c2 $L LIST CONFLICT 1 * write 220200960 221249536
  EXPECT c2 $L LIST CONFLICT 1 * write 222298112 223346688
  EXPECT c2 $L LIST CONFLICT 1 * write 224395264 225443840
  EXPECT c2 $L LIST CONFLICT 1 * write 226492416 227540992
  EXPECT c2 $L LIST CONFLICT 1 * write 228589568 229638144
  EXPECT c2 $L LIST CONFLICT 1 * write 230686720 231735296
  EXPECT c2 $L LIST CONFLICT 1 * write 232783872 233832448
  EXPECT c2 $L LIST CONFLICT 1 * write 234881024 235929600
  EXPECT c2 $L LIST CONFLICT 1 * write 236978176 238026752
  EXPECT c2 $L LIST CONFLICT 1 * write 239075328 240123904
  EXPECT c2 $L LIST CONFLICT 1 * write 241172480 242221056
  EXPECT c2 $L LIST CONFLICT 1 * write 243269632 244318208
  EXPECT c2 $L LIST CONFLICT 1 * write 245366784 246415360
  EXPECT c2 $L LIST CONFLICT 1 * write 247463936 248512512
  EXPECT c2 $L LIST CONFLICT 1 * write 249561088 250609664
  EXPECT c2 $L LIST CONFLICT 1 * write 251658240 252706816
  EXPECT c2 $L LIST CONFLICT 1 * write 253755392 254803968
  EXPECT c2 $L LIST CONFLICT 1 * write 255852544 256901120
  EXPECT c2 $L LIST CONFLICT 1 * write 257949696 258998272
  EXPECT c2 $L LIST CONFLICT 1 * write 260046848 261095424
  EXPECT c2 $L LIST CONFLICT 1 * write 262144000 263192576
  EXPECT c2 $L LIST CONFLICT 1 * write 264241152 265289728
  EXPECT c2 $L LIST CONFLICT 1 * write 266338304 267386880
}
EXPECT c2 $L LIST DENIED 1 0 0
GRANTED c1 UNLOCK 1 0 0
AVAILABLE c2 LIST 1 0 0

QUIT
