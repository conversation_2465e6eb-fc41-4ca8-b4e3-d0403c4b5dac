// SPDX-License-Identifier: LGPL-3.0-or-later
/*
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
 *
 */

/* This file creates LTTNG tracepoints probes. These are the actual trace
 * implementations that will be called when LTTNG is enabled and its shared
 * object is loaded.
 *
 * This file compiles into libganesha_trace.so, which can be dynamically loaded
 * when we want to enable LTTNG. It must include every trace header file
 * once (and only once).
 * When libganesha_trace.so is loaded, the functions generated here overload the
 * weak functions generated by lttng_defines, and allows for LTTNG traces to be
 * sent.
 */

#ifdef USE_LTTNG

#define TRACEPOINT_CREATE_PROBES

#ifndef LTTNG_PARSING
#include "gsh_lttng/generated_traces/generated_lttng.h"
#endif /* LTTNG_PARSING */

#endif /* USE_LTTNG */
