<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>NFS Ganesha</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0">
     <widget class="QTabWidget" name="tabWidget">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="exports_tab">
       <attribute name="title">
        <string>Exports</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="0">
         <widget class="QScrollArea" name="scrollArea">
          <property name="widgetResizable">
           <bool>true</bool>
          </property>
          <widget class="QWidget" name="scrollAreaWidgetContents">
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>0</y>
             <width>770</width>
             <height>507</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_5">
            <item row="0" column="0">
             <widget class="QTableView" name="exports"/>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="clients_tab">
       <attribute name="title">
        <string>Clients</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_3">
        <item row="0" column="0">
         <widget class="QScrollArea" name="scrollArea_2">
          <property name="widgetResizable">
           <bool>true</bool>
          </property>
          <widget class="QWidget" name="scrollAreaWidgetContents_2">
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>0</y>
             <width>770</width>
             <height>507</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_4">
            <item row="0" column="0">
             <widget class="QTableView" name="clients"/>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>800</width>
     <height>21</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>File</string>
    </property>
    <addaction name="actionDBus_connect"/>
    <addaction name="actionQuit"/>
   </widget>
   <widget class="QMenu" name="menuManager">
    <property name="title">
     <string>Manage</string>
    </property>
    <widget class="QMenu" name="menuClients">
     <property name="title">
      <string>Clients</string>
     </property>
     <addaction name="actionAdd_Client"/>
     <addaction name="actionRemove_Client"/>
    </widget>
    <widget class="QMenu" name="menuAdmin">
     <property name="title">
      <string>Admin</string>
     </property>
     <addaction name="actionReset_Grace"/>
     <addaction name="actionShutdown"/>
     <addaction name="actionReload"/>
    </widget>
    <addaction name="actionLog_Settings"/>
    <addaction name="menuClients"/>
    <addaction name="actionExports"/>
    <addaction name="menuAdmin"/>
   </widget>
   <widget class="QMenu" name="menuView">
    <property name="title">
     <string>View</string>
    </property>
    <addaction name="actionStatistics"/>
    <addaction name="actionViewExports"/>
    <addaction name="actionViewClients"/>
   </widget>
   <widget class="QMenu" name="menuHelp">
    <property name="title">
     <string>Help</string>
    </property>
    <addaction name="actionAbout"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuManager"/>
   <addaction name="menuView"/>
   <addaction name="menuHelp"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="actionDBus_connect">
   <property name="text">
    <string>Connect to Ganesha</string>
   </property>
  </action>
  <action name="actionQuit">
   <property name="text">
    <string>Quit</string>
   </property>
  </action>
  <action name="actionExports">
   <property name="text">
    <string>Exports</string>
   </property>
  </action>
  <action name="actionStatistics">
   <property name="text">
    <string>Statistics</string>
   </property>
  </action>
  <action name="actionLog_Levels">
   <property name="text">
    <string>Log Levels</string>
   </property>
  </action>
  <action name="actionViewExports">
   <property name="text">
    <string>Exports</string>
   </property>
  </action>
  <action name="actionAbout">
   <property name="text">
    <string>About</string>
   </property>
  </action>
  <action name="actionReset_Grace">
   <property name="text">
    <string>Reset Grace Period</string>
   </property>
  </action>
  <action name="actionShutdown">
   <property name="text">
    <string>Shutdown</string>
   </property>
  </action>
  <action name="actionReload">
   <property name="text">
    <string>Reload</string>
   </property>
  </action>
  <action name="actionViewClients">
   <property name="text">
    <string>Clients</string>
   </property>
  </action>
  <action name="actionAdd_Client">
   <property name="text">
    <string>Add Client</string>
   </property>
  </action>
  <action name="actionRemove_Client">
   <property name="text">
    <string>Remove Client</string>
   </property>
  </action>
  <action name="actionLog_Settings">
   <property name="text">
    <string>Log Settings</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
