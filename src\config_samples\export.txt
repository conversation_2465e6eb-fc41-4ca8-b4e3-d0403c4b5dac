# Sample export config
#
# This sample is not intended to be used as is, rather it is an illustration of
# some of the flexibility of EXPORT configuration. There are a couple simple
# EXPORT configurations at the end that are more usable.
#
# Options documentation:
#
# Export permission options available in EXPORT_DEFAULTS, EXPORT, and CLIENT
# blocks. If an option is not set in a more specific block, the next less
# specific block will be considered, until finally the default is taken if
# the option is not specified in any applicable block, following this order:
# CLIENT, EXPORT, EXPORT_DEFAULTS, baked in default.
#
# Access_Type (NONE):	RW, RO, MDONLY, MDONLY_RO, NONE
#			RW allows all opertations
#			RO allows only operations that do not modify the server
#			MDONLY does not allow READ or WRITE operations, but
#				allows any other operation.
#			MDONLY_RO does not allow READ, WRITE, or any operation
#				that modifies file attributes or directory
#				content
#			NONE allows no access at all
#
# Protocols (3,4)	The Protocols allowed. NFSV3, NFSV4, and 9P may be
#			specified. 3, 4, V3, V4, NFS3, and NFS4 may also be
#			used.
#
# Transports (UDP, TCP)	The transport protocols allowed (UDP, TCP, and RDMA may
#			be specified)
#
# Squash (Root_Squash)	What kind of user id squashing is performed:
#			No_Root_Squash, NoIdSquash, None
#				No user id squashing is performed
#			RootId, Root_Id_Squash, RootIdSquash
#				uid 0 and gid 0 are squashed to the
#				Anonymous_Uid and Anonymous_Gid
#				gid 0 in alt_groups lists is also squashed
#			Root, Root_Squash, RootSquash
#				uid 0 and gid of any value are squashed to the
#				Anonymous_Uid and Anonymous_Gid
#				alt_groups lists is discarded
#			All, All_Squash, AllSquash, All_Anonymous, AllAnonymous
#				All users are squashed
#
# Anonymous_Uid (-2)	If a user id is squashed, this is the uid used
#			Ranges from -2147483648 to 4294967295
#			uid are traditionally uint32_t however, tradition
#			has long been to specify NFS anonymous uid as -2
#			so negative values are allowed
#
# Anonymous_Gid (-2)	If a group id is squashed, this is the gid used
#			Ranges from -2147483648 to 4294967295
#			gid are traditionally uint32_t however, tradition
#			has long been to specify NFS anonymous gid as -2
#			so negative values are allowed
#
# SecType (none, sys)	The RPC security flavors allowed, none (AUTH_NONE),
#			sys (AUTH_SYS/AUTH_UNIX), krb5 (RPCSEC_GSS),
#			krb5i (RPCSEC_GSS), krb5p (RCSEC_GSS)
#
# PrivilegedPort (false)	If this option is true, client connections
#				must originate from port < 1024. This is
#				tradition based on some operating systems
#				requiring a user to be a privileged user to
#				create a socket with a source port < 1024
#
# Manage_Gids (false)	If this option is true, the alt groups list in
#			AUTH_SYS credentials will be replaced by a server
#			lookup of the group list. This allows bypassing the
#			16 group limit of AUTH_SYS.
#
# Delegations (None)	The types of delegations that may be granted. (None, Read, Write,
#			ReadWrite, R, W, and RW may be specified).


# EXPORT_DEFAULTS block:
#
# All export permission options are usable.
#
# WARNING: If Access_Type is specified, that access type will be granted to
# all clients on any export for which there is not an applicable CLIENT
# block that explicitly provides a different Access_Type or for which the
# EXPORT block does not provide a different Access_Type.
#
# If you desire to set a default Access_Type for all allowed clients, you
# may then want to specify Access_Type = None; in every EXPORT block.

EXPORT_DEFAULTS
{
	SecType = sys, krb5, krb5i, krb5p;

	# Restrict all exports to NFS v4 unless otherwise specified
	Protocols = 4;
}


# EXPORT block
#
# All export permissions options are available, as well as the following:
#
# Export_id (required)	An identifier for the export, must be unique and
#			between 0 and 65535. If Export_Id 0 is specified, Pseudo
#			must be the root path (/).
#
# Path (required)	The directory in the exported file system this export
#			is rooted on (may be ignored for some FSALs). It need
#			not be unique if Pseudo and/or Tag are specified.
#
# Pseudo (required v4)	This option specifies the position in the Pseudo FS
#			this export occupies if this is an NFS v4 export. It
#			must be unique. By using different Pseudo options,
#			the same Path may be exported multiple times.
#
# Tag (no default)	This option allows an alternative access for NFS v3
#			mounts. The option MUST not have a leading /. Clients
#			may not mount subdirectories (i.e. if Tag = foo, the
#			client may not mount foo/baz). By using different
#			Tag options, the same Path may be exported multiple
#			times.
#
# MaxRead (4194304)	The maximum read size on this export
# MaxWrite (4194304)	The maximum write size on this export
# PrefRead (4194304)	The preferred read size on this export
# PrefWrite (4194304)	The preferred write size on this export
# PrefReaddir (16384)	The preferred readdir size on this export
#			These 5 options have the same range of values from
#			512 to 9 megabytes.
#
# MaxOffsetWrite (18446744073709551615)	Maximum file offset that may be written
# MaxOffsetRead (18446744073709551615)	Maximum file offset that may be read
#					These options may be used to restrict
#					the offsets within files.
#
# CLIENT (optional)	See the CLIENT block below
#
# FSAL (required)	See the FSAL block below

EXPORT
{
	Export_Id = 1;
	Path = /export/exp1;
	Pseudo = /export/exp1;
	Tag = exp1;

	# Override the default set in EXPORT_DEFAULTS
	Protocols = 3,4;
	MaxRead = 65536;
	MaxWrite = 65536;
	PrefRead = 65536;
	PrefWrite = 65536;

	# All clients for which there is no CLIENT block that specifies a
	# different Access_Type will have RW access (this would be an unusual
	# specification in the real world since barring a firewall, this
	# export is world readable and writeable).
	Access_Type = RW;

	# FSAL block
	#
	# This is required to indicate which Ganesha File System Abstraction
	# Layer (FSAL) will be used for this export.
	#
	# The only option available for all FSALs is:
	#
	# Name (required)	The name of the FSAL
	#
	# Some FSALs have additional options, see individual FSAL documentation.

	FSAL
	{
		Name = VFS;
	}

	# CLIENT blocks
	#
	# An export may optionally have one or more CLIENT blocks. These blocks
	# specify export options for a restricted set of clients. The export
	# permission options specified in the EXPORT block will apply to any
	# client for which there is no applicable CLIENT block.
	#
	# All export permissions options are available, as well as the
	# following:
	#
	# Clients (required)	The list of clients these export permissions
	#			apply to. Clients may be specified by hostname,
	#			ip address, netgroup, CIDR network address,
	#			host name wild card, or simply "*" to apply to
	#			all clients.

	CLIENT
	{
		Clients = ************, ***********/8;
		Squash = None;
	}

	CLIENT
	{
		# Note the following specification is a larger network than
		# the first block, however, the first applicable CLIENT block
		# is used.
		Clients = ***********/16;
		Squash = All;
		Access_Type = RO;
	}

	CLIENT
	{
		# This block is actually meaningless since ************ will
		# match the network address in the second CLIENT block.
		Clients = ************;
		Squash = None;
		Access_Type = RW;
	}
}

# Here is a simple sample EXPORT that should be used without an EXPORT_DEFAULTS
# block. It takes advantage of the fact that whatever export permissions are
# in the EXPORT block are applied to all clients for which there is no
# matching CLIENT block.

EXPORT
{
	Export_Id = 2;
	Path = /export;
	Pseudo = /export;
	Access_Type = RW;
	Squash = None;
	FSAL
	{
		Name = VFS;
	}
}

# Here is an example with a simple CLIENT block

EXPORT
{
	Export_Id = 3;
	Path = /export2;
	Pseudo = /export2;
	FSAL
	{
		Name = VFS;
	}
	CLIENT
	{
		Clients = your, list, of, clients;
		Access_Type = RW;
		Squash = None;
	}
}

# Finally here is an example of how you can specify options for the Pseudo FS.
# Note that even without specifying the Pseudo Root EXPORT, EXPORT_DEFAULTS will
# still apply to it (except for Access_Type, Protocols, Transports, and Squash,
# since those are all "set" options).

EXPORT
{
	Export_Id - 0;
	Path = /;
	Pseudo = /;
	CLIENT
	{
		Clients = ***********/16;
		Access_Type = MDONLY_RO;
		SecType=sys,krb5,krb5i,krb5p;
	}
}

# The Automatically Generated Pseudo Root is effectively:

EXPORT
{
	Export_Id - 0;
	Path = /;
	Pseudo = /;
	Squash = None;
	Protocols = NFSV4;
	Transports = TCP;
	Access_Type = MDONLY_RO;
	Filesystem_Id = 152.152;
	MaxWrite = 67108864;
	MaxRead = 67108864;
	PrefWrite = 67108864;
	PrefRead = 67108864;
	PrefReaddir = 67108864;
}
