# SPDX-License-Identifier: LGPL-3.0-or-later
# Copyright IBM Corporation, 2012
#  Contributor: <PERSON>  <<EMAIL>>
#
#
# This software is a server that implements the NFS protocol.
#
#
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public
# License as published by the Free Software Foundation; either
# version 3 of the License, or (at your option) any later version.
# 
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# Lesser General Public License for more details.
# 
# You should have received a copy of the GNU Lesser General Public
# License along with this library; if not, write to the Free Software
# Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA


# simple script to test LIST command
{
  # wait for 2 clients to start in any order
  EXPECT c1 * HELLO OK c1
  EXPECT c2 * HELLO OK c2
}
OK c1 OPEN 1 rw temp.out
OK c2 OPEN 1 rw temp.out
GRANTED c1 LOCK 1 write 10 20
GRANTED c1 LOCK 1 read 100 10
c2 $L LIST 1 0 0
{
  # Wait for two locks in any order
  EXPECT c2 $L LIST CONFLICT 1 * write 10 20
  EXPECT c2 $L LIST CONFLICT 1 * read 100 10
}
# List finished with a DENIED if any locks present
EXPECT c2 $L LIST DENIED 1 0 0
QUIT
