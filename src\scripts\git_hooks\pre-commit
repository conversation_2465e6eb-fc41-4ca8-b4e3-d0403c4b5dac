#!/bin/bash
# Ganesha pre-commit hook
#
# 1. Run clang-format on the commit
# 2. Check to see if a submodule is not being updated

# define colors for use in output
red='\033[0;31m'
green='\033[0;32m'
no_color='\033[0m'
grey='\033[0;90m'

#####################
# Clang-Format Hook
#####################

function handle_unformatted_file() {
    set -u
    # If a rebase is in progress just show warning.
    git rebase --show-current-patch 1> /dev/null 2> /dev/null
    if [[ $? == 0 ]]; then
        echo "[git-pre-commit warning] clang-format found formatting missing in commit."
        echo "[git-pre-commit warning] In rebase so just printing the warning without failing the commit."
    else
        echo -e "${red}[git-pre-commit error]${no_color} clang-format found formatting missing in commit. commit aborted."
        exit 1
    fi
}

function check_staged_files() {
    set -u
    local test_command="(cd src; clang-format -style=file --dry-run --Werror -)"
    local retval=0
    for file in $(git diff --cached --name-only --diff-filter=d -- 'src/*.c' 'src/*.h' 'src/*.cpp' 'src/*.hpp' 'src/*.cc')
    do
        git show :${file} | eval "${test_command}"
        if [[ $? != 0 ]]; then
            echo "ERROR: Staged file ${file} is not formatted correctly, please use clang-format to format it"
            retval=1
        fi
    done
    if [[ ${retval} != 0 ]]; then
       handle_unformatted_file
    fi
}

check_staged_files

###############################################################
# Check whether any submodule is about to be updated with the
# commit. Ask the user for confirmation.
###############################################################

echo -e "Checking submodules ${grey}(pre-commit hook)${no_color} "

# Jump to the current project's root directory (the one containing
# .git/)
ROOT_DIR=$(git rev-parse --show-cdup)

SUBMODULES=$(grep path ${ROOT_DIR}.gitmodules | sed 's/^.*path = //')

# Finding the submodules that have been modified
MOD_SUBMODULES=$(git diff --cached --name-only | grep -F "$SUBMODULES")

# If no modified submodules, exit with status code 0, else prompt the
# user and exit accordingly
if [[ -n "$MOD_SUBMODULES" ]]; then
    echo "Submodules to be committed:"
    echo "  (use \"git reset HEAD <file>...\" to unstage)"
    echo

    for SUB in $MOD_SUBMODULES
    do
        echo -e "\t${green}modified:\t$SUB${no_color}"
    done
    echo
    echo -n -e "Continue with commit? ${grey}[N|y]${no_color} "
    read -n 1 reply </dev/tty
    echo
    if [[ "$reply" == "y" || "$reply" == "Y" ]]; then
        echo "Permitting submodules to be committed..."
        exit 0
    else
        echo "Aborting commit due to submodule update."
        exit 1
    fi
fi
exit 0
