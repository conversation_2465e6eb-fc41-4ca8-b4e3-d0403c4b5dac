# Downgrade + notification
#
# Take a write lock from loader 1
# Try to take a non-blocking read lock from loader 2, expect to fail
# Try to take a blocking read lock from loader 2
# Take a read lock from loader 1 (Lock downgrade)
# Expect loader 2 to get the lock in a timely manner
# Release the lock from loader 1
# Release the lock from loader 2
# Test from loader 3 for the list of locks and expect not to see any lock

CLIENTS c1 c2

OK c1 OPEN 1 rw create OFD testFile
OK c2 OPEN 1 rw OFD testFile

# full range
GRANTED c1 LOCK 1 write 0 0
DENIED c2 LOCK 1 read 0 0
OK c1 ALARM 10
c2 $G LOCKW 1 read  0 0
GRANTED c1 LOCK 1 read 0 0
{
    EXPECT c2 $G LOCKW  GRANTED 1 read  0 0
}
c1 * ALARM 0
EXPECT c1 * ALARM CANCELED *
EXPECT c1 * ALARM OK *
GRANTED c1 UNLOCK 1 0 0
GRANTED c2 UNLOCK 1 0 0

# byte range
GRANTED c1 LOCK 1 write 10 10
DENIED c2 LOCK 1 read 10 10
OK c1 ALARM 10
c2 $G LOCKW 1 read  10 10
GRANTED c1 LOCK 1 read 10 10
{
    EXPECT c2 $G LOCKW  GRANTED 1 read  10 10
}
c1 * ALARM 0
EXPECT c1 * ALARM CANCELED *
EXPECT c1 * ALARM OK *
GRANTED c1 UNLOCK 1 0 0
GRANTED c2 UNLOCK 1 0 0

QUIT
