[Unit]
Description=NFS status monitor for NFSv2/3 locking.
DefaultDependencies=no
Conflicts=umount.target
Requires=nss-lookup.target rpcbind.socket
After=network.target nss-lookup.target rpcbind.socket

PartOf=nfs-utils.service

Wants=nfs-config.service rpc-statd-notify.service
After=nfs-config.service

[Service]
EnvironmentFile=-/run/sysconfig/nfs-utils
Type=forking
PIDFile=/run/rpc.statd.pid
ExecStart=/sbin/rpc.statd --no-notify $STATDARGS

[Install]
WantedBy=nfs-server.service
