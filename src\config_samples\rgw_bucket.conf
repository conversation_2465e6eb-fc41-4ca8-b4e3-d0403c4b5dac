EXPORT
{
	Export_ID=1;

	Path = "testbucket";

	Pseudo = "/testbucket";

	Access_Type = RW;

	Protocols = 4;

	Transports = TCP;

	FSAL {
		Name = RGW;
		User_Id = "testuser";
		Access_Key_Id ="<substitute yours>";
		Secret_Access_Key = "<substitute yours>";
	}
}

RGW {
	ceph_conf = "/<substitute path to>/ceph.conf";
	# for vstart cluster, name = "client.admin"
	name = "client.rgw.foohost";
	cluster = "ceph";
#	init_args = "-d --debug-rgw=16";
}
