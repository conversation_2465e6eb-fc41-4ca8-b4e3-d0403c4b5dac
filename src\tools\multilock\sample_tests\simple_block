# SPDX-License-Identifier: LGPL-3.0-or-later
# Copyright IBM Corporation, 2012
#  Contributor: <PERSON>  <<EMAIL>>
#
#
# This software is a server that implements the NFS protocol.
#
#
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public
# License as published by the Free Software Foundation; either
# version 3 of the License, or (at your option) any later version.
# 
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# Lesser General Public License for more details.
# 
# You should have received a copy of the GNU Lesser General Public
# License along with this library; if not, write to the Free Software
# Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA


# This test case attempts a simple blocking lock situation

CLIENTS c1 c2

OK c1 OPEN 1 rw create temp.out
OK c2 OPEN 1 rw temp.out
GRANTED c1 LOCK 1 write 0 10

# wait for conflicting lock
c2 $G LOCKW 1 write 0 10
sleep 2
# now release c1's lock so c2 can get lock
c1 $R UNLOCK 1 0 10
{
  EXPECT c1 $R UNLOCK GRANTED 1 unlock 0 10
  EXPECT c2 $G LOCKW  GRANTED 1 write  0 10
}
sleep 1
GRANTED c2 UNLOCK 1 0 10
QUIT
