# SPDX-License-Identifier: BSD-3-Clause
#-------------------------------------------------------------------------------
#
# Copyright Panasas, 2012
# Contributor: <PERSON> <<EMAIL>>
#
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public
# License as published by the Free Software Foundation; either
# version 3 of the License, or (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public
# License along with this library; if not, write to the Free Software
# Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
#
#-------------------------------------------------------------------------------
# Turn on everything in the options for a complete build

set(PROXYV4_HANDLE_MAPPING ON)
set(USE_DBUS ON)
set(USE_CB_SIMULATOR ON)
set(USE_FSAL_XFS ON)
set(USE_FSAL_CEPH ON)
set(USE_FSAL_RGW ON)
set(USE_FSAL_GLUSTER ON)
set(USE_TOOL_MULTILOCK ON)

message(STATUS "Building everything")
