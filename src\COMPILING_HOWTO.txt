		Building nfs-ganesha From Source
		================================

This HOWTO covers what is necessary to download, configure, and build
the nfs-ganesha NFS server software.

There are two major components to the software, nfs-ganesha itself, and
the libntirpc library which is used for the transport layer.  The server
and the libntirpc library are in separate git repositories.  For the build,
the libntirpc source is a 'git submodule' of nfs-ganesha.

The first step in building is to download (clone) the nfs-ganesha repository
from its official location on github.com into the place in my home directory
where I keep my git trees.

   $ cd ~/git
   $ git clone --recursive https://github.com/nfs-ganesha/nfs-ganesha.git

Note the '--recursive' option which clones and initializes the submodule
for libntirpc.  If you do not use this option, you will have to use the
git submodule command below before building.  If you forget, the 'cmake'
step below will terminate with an error message reminding you that you
forgot.

The git submodule
-----------------

If you are building from a release source archive rather than from
git, skip to the next section.  However, if you are building from git,
you must initialize the submodule after clone and after pulling a new
update by going to the root of your repository and typing

  git submodule update --init

Also, please note that the current HEAD of the ntirpc directory is a
piece of tracked state.  Please do not commit a change to the state
unintentionally.

NOTE:
	The submodule can get out of step with the main project.  This
	can occur if you make a change to the nfs-ganesha working
	directory such as doing a checkout of another branch or a rebase
	of your work from an upstream update. In these cases you must
	do a 'git update' to make sure you are building nfs-ganesha with
	the correct version of libntirpc.

CMAKE Build Instructions
------------------------

Cmake can and does prefer to build out- of-source.  In other words,
your build tree is over here and your git source tree is over there.
The Makefiles are created by Cmake in the build tree, the objects and
targets are in the build tree but the source is referenced "over
there".  For example, in a Ganesha build, we would do:

   $ cd some-build-sandbox
   $ rm -rf build_dir; mkdir build_dir
   $ cd build_dir
   $ cmake ~/git/nfs-ganesha/src
   $ make
   $ make install

This gets the build completely away from where the git repo is. Note
that I have thoroughly scrubbed the area before doing the build.  You can
also build in-tree but this litters the git repo with extra files just like
autotools.  See the Cmake manual for the details and restrictions.

Building is a two step process.  You first run Cmake to configure the build
and then you do a conventional Make.  You can do iterative development by
editing files, including Cmake files (CMakeLists.txt) in the source tree
and go back to the build directory and do a "make".  The makefile will do the
right thing and re-run Cmake if you messed with configuration files.  Your
configuration and build parameters are preserved in the build tree so you
only have to do the full configuration step once.

Unlike autotools where the build and source are in the same tree, having a
separate build area allows you to do a couple of thing safely.

  * You can delete the whole build tree at any time.  Simply repeat the
    configuration step and you get it all back.  Your source is safely
    somewhere else.  Be aware of which window/terminal you are in before
    doing an "rm -rf" however.  Yes, I did that once so now I have the
    windows on separate monitors...

  * You can easily build multiple configurations.  Simply create one build
    directory, enter it, and run Cmake with one set of parameters.  Repeat
    in another build directory with a different set of parameters.  Nice.

System-wide NTIRPC
------------------

libntirpc may be installed separately on the system, and ganesha build with the
-DUSE_SYSTEM_NTIRPC option.  This does not require the submodule, and will not
build ntirpc.

Configuration Tweaking
----------------------
Cmake allows the setting of configuration parameters from the command line.
You would use this in a similar way to how autotools works.

You can discover what you can tweak by doing the following:

   $ mkdir tweaktest; cd tweaktest
   $ cmake -i ~/git/nfs-ganesha/src

This will enter you into a "wizard" configuration (no fancy GUI stuff).
Simply step through the configuration and note what knobs and switches
are available and what their defaults are.  After this, you can explicitly
change parameters from the command line.  For example:

   $ mkdir mybuild; cd mybuild
   $ cmake -D_USE_9P=OFF -D_HANDLE_MAPPING=ON -DALLOCATOR=tcmalloc \
     ~/git/nfs-ganesha/src

This will disable a 9P build, use handle mapping in the PROXY fsal and
pick the tcmalloc allocator.

There are two other variables of interest:

CMAKE_BUILD_TYPE
   This is a global setting for the type of build.  See the Cmake documentation
   for what this means.  I have added a "Maintainer" type which forces strict
   compiles.  It is what I intend to use on builds.

BUILD_CONFIG
   This setting triggers the loading of a file in src/cmake/build_configurations.
   This is useful for having a "canned" configuration.  There is only one
   file currently in use which will turn on every option available.

Put these together and you get the build I use for merge testing:

  $ cmake -DCMAKE_BUILD_TYPE=Maintainer -DBUILD_CONFIG=everything \
    ~/git/nfs-ganesha/src

Look at src/cmake/build_configuration/everything.cmake to see what this turns
on.  If you want a custom, for say just a 9P server or only some features,
create a file on the model of everything.cmake in that directory and then
reference it on the command line.  This eliminates the various shell scripts
we have laying around...  I stole this from the Mysql build where they use
this trick to have things like 'redhat.cmake' and 'debian.cmake'.
