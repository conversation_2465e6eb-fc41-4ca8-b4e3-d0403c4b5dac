# SPDX-License-Identifier: LGPL-3.0-or-later
# Copyright IBM Corporation, 2012
#  Contributor: <PERSON>  <<EMAIL>>
#
#
# This software is a server that implements the NFS protocol.
#
#
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public
# License as published by the Free Software Foundation; either
# version 3 of the License, or (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public
# License along with this library; if not, write to the Free Software
# Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
#
CLIENTS client1

FORK client1 client2

OK client1 OPEN 1 rw create lock1
OK client2 OPEN 1 rw lock1

GRANTED client1 LOCK 1 write 0 100
GRANTED client2 LOCK 1 write 100 100

# Now test the locks

client1 $ TEST 1 read 0 100
EXPECT client1 $ TEST GRANTED 1 read 0 100
client1 $ TEST 1 read 100 100
EXPECT client1 $ TEST CONFLICT 1 * write 100 100

client2 $ TEST 1 read 0 100
EXPECT client2 $ TEST CONFLICT 1 * write 0 100
client2 $ TEST 1 read 100 100
EXPECT client2 $ TEST GRANTED 1 read 100 100

OK client2 QUIT
OK client1 QUIT

QUIT
