# This isn't actually a Linux kernel tree
--no-tree

# Errors that make no sense if we aren't the Linux kernel
--ignore MOD<PERSON>IED_INCLUDE_ASM
--ignore LO_MACRO
--ignore HI_MACRO
--ignore CSYNC
--ignore SSYNC
--ignore U<PERSON>I_INCLUDE
--ignore IN_ATOMIC
--ignore LOCKDEP

# Warnings specitifc to the Linux kernel
--ignore USE_RELATIVE_PATH
--ignore CONFIG_DESCRIPTION
--ignore CONFIG_EXPERIMENTAL
--ignore DEPRECATED_VARIABLE
--ignore NETWORKING_BLOCK_COMMENT_STYLE
--ignore HOTPLUG_SECTION
--ignore EXPORT_SYMBOL
--ignore DEFINE_PCI_DEVICE_TABLE
--ignore LINUX_VERSION_CODE
--ignore PRINTK_RATELIMITED
--ignore PRINTK_WITHOUT_KERN_LEVEL
--ignore PREFER_PR_LEVEL
--ignore USE_NEGATIVE_ERRNO
--ignore INCLUDE_LINUX
--ignore MISSING_VMLINUX
--ignore MS<PERSON>EP
--ignore PREFER_PACKED
--ignore PREFER_ALIGNED
--ignore PREFER_PRINTF
--ignore PREFER_SCANF
--ignore USE_SPINLOCK_T
--ignore PREFER_SEQ_PUTS
--ignore USLEEP_RANGE
--ignore KREALLOC_ARG_REUSE
--ignore YIELD
--ignore CONSIDER_KSTRTO
--ignore USE_DEVICE_INITCALL
--ignore NR_CPUS
--ignore PRINTF_L
--ignore EXPORTED_WORLD_WRITABLE
--ignore FILE_PATH_CHANGES

# Checks specitifc to the Linux kernel
--ignore ARCH_INCLUDE_LINUX
--ignore UNCOMMENTED_DEFINITION
--ignore UNCOMMENTED_BARRIER
--ignore ARCH_DEFINES
--ignore UNDOCUMENTED_SETUP
--ignore NEW_TYPEDEFS

# Don't flag this, since we have LogThis and FSAL_that.
--ignore CAMELCASE

# Do not consider gerrit changeid as an error
--ignore GERRIT_CHANGE_ID

# Not being in the kernel, we don't have ARRAY_SIZE macro so some places
# define it.
--ignore ARRAY_SIZE

# our coding style has too many if-then-else that have return in the middle
# and thus create unnecessary else
--ignore UNNECESSARY_ELSE

# Allow janitor patches to cleanup checkpatch errors
--ignore EMAIL_SUBJECT

# We use full commit id when referencing other commits
--ignore GIT_COMMIT_ID

# We have the FSF address in a lot of our headers at the moment,
# should clean that up one day but ignoring for now.
--ignore FSF_MAILING_ADDRESS

# We will no longer get grumpy about single line braces
--ignore BRACES

# We use #if 0 to preserve possibly interesting code
--ignore IF_0
--ignore IF_1

--ignore PREFER_DEFINED_ATTRIBUTE_MACRO
--ignore STRLCPY
--ignore DATE_TIME

# @todo FSF work on removing these ones if they make sense...
--ignore BLOCK_COMMENT_STYLE
#--ignore GLOBAL_INITIALISERS
#--ignore UNSPECIFIED_INT
#--ignore LONG_LINE
#--ignore SUSPECT_CODE_INDENT
#--ignore CONSTANT_COMPARISON
#--ignore LINE_SPACING
#--ignore TYPECAST_INT_CONSTANT

# allow split strings
--ignore SPLIT_STRING

# we like symbolic permissions
--ignore SYMBOLIC_PERMS

# we are ok with function prototypes without identifier names
--ignore FUNCTION_ARGUMENTS

--ignore CONST_STRUCT

#retain 80 column max line length
 --max-line-length=80
