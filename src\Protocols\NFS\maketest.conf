
Test mount_protocol
{
   Product = Mount protocol.
   Command = ../../bin/`archi -M`/test_mntproto
   Comment = Testing mount protocol.

        # all tests OK
        Success TestOk
        {
          STDOUT =~ /test_mnt_Null : OK/
          AND
          STDOUT =~ /test_mnt_Export : OK/
        }
        
        # Command execution problem
        Failure ERROR_MNTPROC_NULL
        {
          NOT
          STDOUT =~ /test_mnt_Null : OK/
        }

        # Command execution problem
        Failure ERROR_MNTPROC_EXPORT
        {
          NOT
          STDOUT =~ /test_mnt_Export : OK/
        }
                       
        # other problem ?
        Failure UNKNOWN_ERROR
        {
          STDOUT =~ /ERROR/
        }               
        
        # abnormal termination (should return 0)
        Failure AbnormalTermination
        {
          STATUS != 0
        }
}
