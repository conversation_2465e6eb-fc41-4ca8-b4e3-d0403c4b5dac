// SPDX-License-Identifier: MIT
/* city-test.c - cityhash-c
 * CityHash on C
 * Copyright (c) 2011-2012, <PERSON>
 *
 * - original copyright notice -
 * Copyright (c) 2011 Google, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

#include <string.h>
#include <stdio.h>

#include "city.h"
#ifdef __SSE4_2__
#include "citycrc.h"
#endif

static const uint64 k0 = 0xc3a5c85c97cb3127ULL;
static const uint64 kSeed0 = 1234567;
static const uint64 kSeed1 = 0xc3a5c85c97cb3127ULL;
static const uint128 kSeed128 = { 1234567, 0xc3a5c85c97cb3127ULL };

static const int kDataSize = 1 << 20;
static const int kTestSize = 300;

static char data[1 << 20];

static int errors; /* global error count */

/* Initialize data to pseudorandom values.
 */
void setup(void)
{
	uint64 a = 9;
	uint64 b = 777;
	int i;

	for (i = 0; i < kDataSize; i++) {
		a = (a ^ (a >> 41)) * k0 + b;
		b = (b ^ (b >> 41)) * k0 + i;

		uint8 u = b >> 37;

		memcpy(data + i, &u, 1); /* uint8 -> char */
	}
}

#define C(x) (0x##x##ULL)

static const uint64 testdata[300][15] = {
	{ C(9 ae16a3b2f90404f), C(75106 db890237a4a), C(3f eac5f636039766),
	  C(3df 09df c64c09a2b), C(3 cb540c392e51e29), C(6 b56343feac0663),
	  C(5 b7bc50fd8e8ad92), C(3df 09df c64c09a2b), C(3 cb540c392e51e29),
	  C(6 b56343feac0663), C(5 b7bc50fd8e8ad92), C(889f 555 a0f5b2dc0),
	  C(7767800902 c8a8ce), C(bcd2a808f4cb4a44), C(e9024dba8f94f2f3) },
	{ C(75e9 dee28ded761d), C(931992 c1b14334c5), C(245ee b25ba2c172e),
	  C(1290f 0e8 a5caa74d), C(ca4c6bf7583f5cda), C(e1d60d51632c536d),
	  C(cbc54a1db641910a), C(1290f 0e8 a5caa74d), C(ca4c6bf7583f5cda),
	  C(e1d60d51632c536d), C(cbc54a1db641910a), C(9866 d68d17c2c08e),
	  C(8 d84ba63eb4d020a), C(df0ad99c78cbce44), C(7 c98593ef62573ed) },
	{ C(75 de892fdc5ba914), C(f89832e71f764c86), C(39 a82df1f278a297),
	  C(b4af8ae673acb930), C(992 b7acb203d8885), C(57 b533f3f8b94d50),
	  C(bbb69298a5dcf1a1), C(b4af8ae673acb930), C(992 b7acb203d8885),
	  C(57 b533f3f8b94d50), C(bbb69298a5dcf1a1), C(433495196 af9ac4f),
	  C(53445 c0896ae1fe6), C(f7b939315f6fb56f), C(ac1b05e5a2e0335e) },
	{ C(69 cfe9fca1cc683a), C(e65f2a81e19b8067), C(20575ea6370 a9d14),
	  C(8f 52532f c6f005b7), C(4eb e60df371ec129), C(c6ef8a7f8deb8116),
	  C(83df 17e3 c9bb9a67), C(8f 52532f c6f005b7), C(4eb e60df371ec129),
	  C(c6ef8a7f8deb8116), C(83df 17e3 c9bb9a67), C(6 a0aaf51016e19cd),
	  C(fb0d1e89f39dbf6a), C(c73095102872943a), C(405ea97456 c28a75) },
	{ C(675 b04c582a34966), C(53624 b5ef8cd4f45), C(c412e0931ac8c9b1),
	  C(798637e677 c65a3), C(83e3 b06adc4cd3ff), C(f3e76e8a7135852f),
	  C(111e66 cfbb05366d), C(798637e677 c65a3), C(83e3 b06adc4cd3ff),
	  C(f3e76e8a7135852f), C(111e66 cfbb05366d), C(29 c4f84aa48e8682),
	  C(b77a8685750c94d0), C(7 cab65571969123f), C(fb1dbd79f68a8519) },
	{ C(46f a817397ea8b68), C(cc960c1c15ce2d20), C(e5f9f947bafb9e79),
	  C(b342cdf0d7ac4b2a), C(66914 d44b373b232), C(261194e76 cb43966),
	  C(45 a0010190365048), C(b342cdf0d7ac4b2a), C(66914 d44b373b232),
	  C(261194e76 cb43966), C(45 a0010190365048), C(e2586947ca8eac83),
	  C(6650 daf2d9677cdc), C(2f 9533 d8f4951a9), C(a5bdc0f3edc4bd7b) },
	{ C(406e959 cdffadec7), C(e80dc125dca28ed1), C(e5beb146d4b79a21),
	  C(e66d5c1bb441541a), C(d14961bc1fd265a2), C(e4cc669d4fc0577f),
	  C(abf4a51e36da2702), C(e66d5c1bb441541a), C(d14961bc1fd265a2),
	  C(e4cc669d4fc0577f), C(abf4a51e36da2702), C(21236 d12df338f75),
	  C(54 b8c4a5ad2ae4a4), C(202 d50ef9c2d4465), C(5ec c6a128e51a797) },
	{ C(46663908 b4169b95), C(4e7 e90b5c426bf1d), C(dc660b58daaf8b2c),
	  C(b298265ebd1bd55f), C(4 a5f6838b55c0b08), C(fc003c97aa05d397),
	  C(2f b5adad3380c3bc), C(b298265ebd1bd55f), C(4 a5f6838b55c0b08),
	  C(fc003c97aa05d397), C(2f b5adad3380c3bc), C(c46fd01d253b4a0b),
	  C(4 c799235c2a33188), C(7e21 bc57487a11bf), C(e1392bb1994bd4f2) },
	{ C(f214b86cffeab596), C(5f ccb0b132da564f), C(86e7 aa8b4154b883),
	  C(763529 c8d4189ea8), C(860 d77e7fef74ca3), C(3 b1ba41191219b6b),
	  C(722 b25dfa6d0a04b), C(763529 c8d4189ea8), C(860 d77e7fef74ca3),
	  C(3 b1ba41191219b6b), C(722 b25dfa6d0a04b), C(5f 7 b463094e22a91),
	  C(75 d6f57376b31bd7), C(d253c7f89efec8e6), C(efe56ac880a2b8a3) },
	{ C(eba670441d1a4f7d), C(eb6b272502d975fa), C(69f 8 d424d50c083e),
	  C(313 d49cb51b8cd2c), C(6e982 d8b4658654a), C(dd59629a17e5492d),
	  C(81 cb23bdab95e30e), C(313 d49cb51b8cd2c), C(6e982 d8b4658654a),
	  C(dd59629a17e5492d), C(81 cb23bdab95e30e), C(1e6 c3e6c454c774f),
	  C(177655172666 d5ea), C(9 cc67e0d38d80886), C(36 a2d64d7bc58d22) },
	{ C(172 c17ff21dbf88d), C(1f 5104e320f 0 c815), C(1e34 e9f1fa63bcef),
	  C(3506 ae8fae368d2a), C(59f a2b2de5306203), C(67 d1119dcfa6007e),
	  C(1f 7190 c648ad9aef), C(3506 ae8fae368d2a), C(59f a2b2de5306203),
	  C(67 d1119dcfa6007e), C(1f 7190 c648ad9aef), C(7e8 b1e689137b637),
	  C(cbe373368a31db3c), C(dbc79d82cd49c671), C(641399520 c452c99) },
	{ C(5 a0838df8a019b8c), C(73f c859b4952923), C(45e39 daf153491bd),
	  C(a9b91459a5fada46), C(de0fbf8800a2da3), C(21800e4 b5af9dedb),
	  C(517 c3726ae0dbae7), C(a9b91459a5fada46), C(de0fbf8800a2da3),
	  C(21800e4 b5af9dedb), C(517 c3726ae0dbae7), C(1 ccffbd74acf9d66),
	  C(cbb08cf95e7eda99), C(61444f 09e2 a29587), C(35 c0d15745f96455) },
	{ C(8f 42 b1fbb2fc0302), C(5 ae31626076ab6ca), C(b87f0cb67cb75d28),
	  C(2498586 ac2e1fab2), C(e683f9cbea22809a), C(a9728d0b2bbe377c),
	  C(46 baf5cae53dc39a), C(2498586 ac2e1fab2), C(e683f9cbea22809a),
	  C(a9728d0b2bbe377c), C(46 baf5cae53dc39a), C(806f 4352 c99229e),
	  C(d4643728fc71754a), C(998 c1647976bc893), C(d8094fdc2d6bb032) },
	{ C(72085e82 d70dcea9), C(32f 502 c43349ba16), C(5eb c98c3645a018f),
	  C(c7fa762238fd90ac), C(8 d03b5652d615677), C(a3f5226e51d42217),
	  C(46 d5010a7cae8c1e), C(c7fa762238fd90ac), C(8 d03b5652d615677),
	  C(a3f5226e51d42217), C(46 d5010a7cae8c1e), C(4293122580 db7f5f),
	  C(3df 6042f 39 c6d487), C(439124809 cf5c90e), C(90 b704e4f71d0ccf) },
	{ C(32 b75fc2223b5032), C(246f ff80eb230868), C(a6fdbc82c9aeecc0),
	  C(c089498074167021), C(ab094a9f9ab81c23), C(4f acf3d9466bcb03),
	  C(57 aa9c67938cf3eb), C(c089498074167021), C(ab094a9f9ab81c23),
	  C(4f acf3d9466bcb03), C(57 aa9c67938cf3eb), C(79 a769ca1c762117),
	  C(9 c8dee60337f87a8), C(dabf1b96535a3abb), C(f87e9fbb590ba446) },
	{ C(e1dd010487d2d647), C(12352858295 d2167), C(acc5e9b6f6b02dbb),
	  C(1 c66ceea473413df), C(dc3f70a124b25a40), C(66 a6dfe54c441cd8),
	  C(b436dabdaaa37121), C(1 c66ceea473413df), C(dc3f70a124b25a40),
	  C(66 a6dfe54c441cd8), C(b436dabdaaa37121), C(6 d95aa6890f51674),
	  C(42 c6c0fc7ab3c107), C(83 b9dfe082e76140), C(939 cdbd3614d6416) },
	{ C(2994f 9245194 a7e2), C(b7cd7249d6db6c0c), C(2170 a7d119c5c6c3),
	  C(8505 c996b70ee9fc), C(b92bba6b5d778eb7), C(4 db4c57f3a7a4aee),
	  C(3 cfd441cb222d06f), C(8505 c996b70ee9fc), C(b92bba6b5d778eb7),
	  C(4 db4c57f3a7a4aee), C(3 cfd441cb222d06f), C(4 d940313c96ac6bd),
	  C(43762837 c9ffac4b), C(480f cf58920722e3), C(4 bbd1e1a1d06752f) },
	{ C(32e2 ed6fa03e5b22), C(58 baf09d7c71c62b), C(a9c599f3f8f50b5b),
	  C(1660 a2c4972d0fa1), C(1 a1538d6b50a57c), C(8 a5362485bbc9363),
	  C(e8eec3c84fd9f2f8), C(1660 a2c4972d0fa1), C(1 a1538d6b50a57c),
	  C(8 a5362485bbc9363), C(e8eec3c84fd9f2f8), C(2562514461 d373da),
	  C(33857675f ed52b4), C(e58d2a17057f1943), C(fe7d3f30820e4925) },
	{ C(37 a72b6e89410c9f), C(139f ec53b78cee23), C(4f ccd8f0da7575c3),
	  C(3 a5f04166518ac75), C(f49afe05a44fc090), C(cb01b4713cfda4bd),
	  C(9027 bd37ffc0a5de), C(3 a5f04166518ac75), C(f49afe05a44fc090),
	  C(cb01b4713cfda4bd), C(9027 bd37ffc0a5de), C(e15144d3ad46ec1b),
	  C(736f d99679a5ae78), C(b3d7ed9ed0ddfe57), C(cef60639457867d7) },
	{ C(10836563 cb8ff3a1), C(d36f67e2dfc085f7), C(edc1bb6a3dcba8df),
	  C(bd4f3a0566df3bed), C(81f c8230c163dcbe), C(4168 bc8417a8281b),
	  C(7100 c9459827c6a6), C(bd4f3a0566df3bed), C(81f c8230c163dcbe),
	  C(4168 bc8417a8281b), C(7100 c9459827c6a6), C(21 cad59eaf79e72f),
	  C(61 c8af6fb71469f3), C(b0dfc42ce4f578b), C(33ea34 ccea305d4e) },
	{ C(4 dabcb5c1d382e5c), C(9 a868c608088b7a4), C(7 b2b6c389b943be5),
	  C(c914b925ab69fda0), C(6 bafe864647c94d7), C(7 a48682dd4afa22),
	  C(40f e01210176ba10), C(c914b925ab69fda0), C(6 bafe864647c94d7),
	  C(7 a48682dd4afa22), C(40f e01210176ba10), C(88dd 28f 33ec31388),
	  C(c6db60abf1d45381), C(7 b94c447298824d5), C(6 b2a5e05ad0b9fc0) },
	{ C(296 afb509046d945), C(c38fe9eb796bd4be), C(d7b17535df110279),
	  C(dd2482b87d1ade07), C(662785 d2e3e78ddf), C(eae39994375181bb),
	  C(9994500 c077ee1db), C(dd2482b87d1ade07), C(662785 d2e3e78ddf),
	  C(eae39994375181bb), C(9994500 c077ee1db), C(a275489f8c6bb289),
	  C(30695ea31df 1 a369), C(1 aeeb31802d701b5), C(7799 d5a6d5632838) },
	{ C(f7c0257efde772ea), C(af6af9977ecf7bff), C(1 cdff4bd07e8d973),
	  C(fab1f4acd2cd4ab4), C(b4e19ba52b566bd), C(7f 1 db45725fe2881),
	  C(70276f f8763f8396), C(fab1f4acd2cd4ab4), C(b4e19ba52b566bd),
	  C(7f 1 db45725fe2881), C(70276f f8763f8396), C(1 b0f2b546dddd16b),
	  C(aa066984b5fd5144), C(7 c3f9386c596a5a8), C(e5befdb24b665d5f) },
	{ C(61e021 c8da344ba1), C(cf9c720676244755), C(354f fa8e9d3601f6),
	  C(44e40 a03093fbd92), C(bda9481cc5b93cae), C(986 b589cbc0cf617),
	  C(210f 59f 074044831), C(44e40 a03093fbd92), C(bda9481cc5b93cae),
	  C(986 b589cbc0cf617), C(210f 59f 074044831), C(ac32cbbb6f50245a),
	  C(afa6f712efb22075), C(47289f 7 af581719f), C(31 b6e75d3aa0e54b) },
	{ C(c0a86ed83908560b), C(440 c8b6f97bd1749), C(a99bf2891726ea93),
	  C(ac0c0b84df66df9d), C(3ee2337 b437eb264), C(8 a341daed9a25f98),
	  C(cc665499aa38c78c), C(ac0c0b84df66df9d), C(3ee2337 b437eb264),
	  C(8 a341daed9a25f98), C(cc665499aa38c78c), C(af7275299d79a727),
	  C(874f a8434b45d0e), C(ca7b67388950dd33), C(2 db5cd3675ec58f7) },
	{ C(35 c9cf87e4accbf3), C(2267eb4 d2191b2a3), C(80217695666 b2c9),
	  C(cd43a24abbaae6d), C(a88abf0ea1b2a8ff), C(e297ff01427e2a9d),
	  C(935 d545695b2b41d), C(cd43a24abbaae6d), C(a88abf0ea1b2a8ff),
	  C(e297ff01427e2a9d), C(935 d545695b2b41d), C(6 accd4dbcb52e849),
	  C(261295 acb662fd49), C(f9d91f1ac269a8a2), C(5e45f 39df 355e395) },
	{ C(e74c366b3091e275), C(522e657 c5da94b06), C(ca9afa806f1a54ac),
	  C(b545042f67929471), C(90 d10e75ed0e75d8), C(3ea60f 8f 158df 77e),
	  C(8863eff 3 c2d670b7), C(b545042f67929471), C(90 d10e75ed0e75d8),
	  C(3ea60f 8f 158df 77e), C(8863eff 3 c2d670b7), C(5799296e97f 144 a7),
	  C(1 d6e517c12a88271), C(da579e9e1add90ef), C(942f b4cdbc3a4da) },
	{ C(a3f2ca45089ad1a6), C(13f 6270f e56fbce4), C(1f 93 a534bf03e705),
	  C(aaea14288ae2d90c), C(1 be3cd51ef0f15e8), C(e8b47c84d5a4aac1),
	  C(297 d27d55b766782), C(aaea14288ae2d90c), C(1 be3cd51ef0f15e8),
	  C(e8b47c84d5a4aac1), C(297 d27d55b766782), C(e922d1d8bb2afd0),
	  C(b4481c4fa2e7d8d5), C(691e21538 af794d5), C(9 bd4fb0a53962a72) },
	{ C(e5181466d8e60e26), C(cf31f3a2d582c4f3), C(d9cee87cb71f75b2),
	  C(4750 ca6050a2d726), C(d6e6dd8940256849), C(f3b3749fdab75b0),
	  C(c55d8a0f85ba0ccf), C(4750 ca6050a2d726), C(d6e6dd8940256849),
	  C(f3b3749fdab75b0), C(c55d8a0f85ba0ccf), C(47f 134f 9544 c6da6),
	  C(e1cdd9cb74ad764), C(3 ce2d096d844941e), C(321f e62f608d2d4e) },
	{ C(fb528a8dd1e48ad7), C(98 c4fd149c8a63dd), C(4 abd8fc3377ae1f),
	  C(d7a9304abbb47cc5), C(7f 2 b9a27aa57f99), C(353 ab332d4ef9f18),
	  C(47 d56b8d6c8cf578), C(d7a9304abbb47cc5), C(7f 2 b9a27aa57f99),
	  C(353 ab332d4ef9f18), C(47 d56b8d6c8cf578), C(df55f58ae09f311f),
	  C(dba9511784fa86e3), C(c43ce0288858a47e), C(62971e94270 b78e1) },
	{ C(da6d2b7ea9d5f9b6), C(57 b11153ee3b4cc8), C(7 d3bd1256037142f),
	  C(90 b16ff331b719b5), C(fc294e7ad39e01e6), C(d2145386bab41623),
	  C(7045 a63d44d76011), C(90 b16ff331b719b5), C(fc294e7ad39e01e6),
	  C(d2145386bab41623), C(7045 a63d44d76011), C(a232222ed0fe2fa4),
	  C(e6c17dff6c323a8a), C(bbcb079be123ac6c), C(4121f e2c5de7d850) },
	{ C(61 d95225bc2293e), C(f6c52cb6be9889a8), C(91 a0667a7ed6a113),
	  C(441133 d221486a3d), C(fb9c5a40e19515b), C(6 c967b6c69367c2d),
	  C(145 bd9ef258c4099), C(441133 d221486a3d), C(fb9c5a40e19515b),
	  C(6 c967b6c69367c2d), C(145 bd9ef258c4099), C(a0197657160c686e),
	  C(91 ada0871c23f379), C(2f d74fceccb5c80c), C(bf04f24e2dc17913) },
	{ C(81247 c01ab6a9cc1), C(fbccea953e810636), C(ae18965000c31be0),
	  C(15 bb46383daec2a5), C(716294063 b4ba089), C(f3bd691ce02c3014),
	  C(14 ccaad685a20764), C(15 bb46383daec2a5), C(716294063 b4ba089),
	  C(f3bd691ce02c3014), C(14 ccaad685a20764), C(5 db25914279d6f24),
	  C(25 c451fce3b2ed06), C(e6bacb43ba1ddb9a), C(6 d77493a2e6fd76) },
	{ C(c17f3ebd3257cb8b), C(e9e68c939c118c8d), C(72 a5572be35bfc1b),
	  C(f6916c341cb31f2a), C(591 da1353ee5f31c), C(f1313c98a836b407),
	  C(e0b8473eada48cd1), C(f6916c341cb31f2a), C(591 da1353ee5f31c),
	  C(f1313c98a836b407), C(e0b8473eada48cd1), C(ac5c2fb40b09ba46),
	  C(3 a3e8a9344eb6548), C(3 bf9349a9d8483a6), C(c30dd0d9b15e92d0) },
	{ C(9802438969 c3043b), C(6 cd07575c948dd82), C(83e26 b6830ea8640),
	  C(d52f1fa190576961), C(11 d182e4f0d419cc), C(5 d9ccf1b56617424),
	  C(c8a16debb585e452), C(d52f1fa190576961), C(11 d182e4f0d419cc),
	  C(5 d9ccf1b56617424), C(c8a16debb585e452), C(2158 a752d2686d40),
	  C(b93c1fdf54789e8c), C(3 a9a435627b2a30b), C(de6e5e551e7e5ad5) },
	{ C(3dd 8ed248 a03d754), C(d8c1fcf001cb62e0), C(87 a822141ed64927),
	  C(4 bfaf6fd26271f47), C(aefeae8222ad3c77), C(cfb7b24351a60585),
	  C(8678904e9 e890b8f), C(4 bfaf6fd26271f47), C(aefeae8222ad3c77),
	  C(cfb7b24351a60585), C(8678904e9 e890b8f), C(968dd 1 aa4d7dcf31),
	  C(7 ac643b015007a39), C(d1e1bac3d94406ec), C(babfa52474a404fa) },
	{ C(c5bf48d7d3e9a5a3), C(8f 0249 b5c5996341), C(c6d2c8a606f45125),
	  C(fd1779db740e2c48), C(1950ef50f efab3f8), C(e4536426a6196809),
	  C(699556 c502a01a6a), C(fd1779db740e2c48), C(1950ef50f efab3f8),
	  C(e4536426a6196809), C(699556 c502a01a6a), C(2f 49 d268bb57b946),
	  C(b205baa6c66ebfa5), C(ab91ebe4f48b0da1), C(c7e0718ccc360328) },
	{ C(bc4a21d00cf52288), C(28df 3eb5 a533fa87), C(6081 bbc2a18dd0d),
	  C(8ee d355d219e58b9), C(2 d7b9f1a3d645165), C(5758 d1aa8d85f7b2),
	  C(9 c90c65920041dff), C(8ee d355d219e58b9), C(2 d7b9f1a3d645165),
	  C(5758 d1aa8d85f7b2), C(9 c90c65920041dff), C(3 c5c4ea46645c7f1),
	  C(346879ec c0e2eb90), C(8434f ec461bb5a0f), C(783 ccede50ef5ce9) },
	{ C(172 c8674913ff413), C(1815 a22400e832bf), C(7e011f 9467 a06650),
	  C(161 be43353a31dd0), C(79 a8afddb0642ac3), C(df43af54e3e16709),
	  C(6e12553 a75b43f07), C(161 be43353a31dd0), C(79 a8afddb0642ac3),
	  C(df43af54e3e16709), C(6e12553 a75b43f07), C(3 ac1b1121e87d023),
	  C(2 d47d33df7b9b027), C(e2d3f71f4e817ff5), C(70 b3a11ca85f8a39) },
	{ C(17 a361dbdaaa7294), C(c67d368223a3b83c), C(f49cf8d51ab583d2),
	  C(666eb21 e2eaa596), C(778f 3e1 b6650d56), C(3f 6 be451a668fe2d),
	  C(5452892 b0b101388), C(666eb21 e2eaa596), C(778f 3e1 b6650d56),
	  C(3f 6 be451a668fe2d), C(5452892 b0b101388), C(cc867fceaeabdb95),
	  C(f238913c18aaa101), C(f5236b44f324cea1), C(c507cc892ff83dd1) },
	{ C(5 cc268bac4bd55f), C(232717 a35d5b2f1), C(38 da1393365c961d),
	  C(2 d187f89c16f7b62), C(4eb504204f a1be8), C(222 bd53d2efe5fa),
	  C(a4dcd6d721ddb187), C(2 d187f89c16f7b62), C(4eb504204f a1be8),
	  C(222 bd53d2efe5fa), C(a4dcd6d721ddb187), C(d86bbe67666eca70),
	  C(c8bbae99d8e6429f), C(41 dac4ceb2cb6b10), C(2f 90 c331755f6c48) },
	{ C(db04969cc06547f1), C(fcacc8a75332f120), C(967 ccec4ed0c977e),
	  C(ac5d1087e454b6cd), C(c1f8b2e284d28f6c), C(cc3994f4a9312cfa),
	  C(8 d61606dbc4e060d), C(ac5d1087e454b6cd), C(c1f8b2e284d28f6c),
	  C(cc3994f4a9312cfa), C(8 d61606dbc4e060d), C(17315 af3202a1307),
	  C(850775145e01163 a), C(96f 10e7357f 930 d2), C(abf27049cf07129) },
	{ C(25 bd8d3ca1b375b2), C(4 ad34c2c865816f9), C(9 be30ad32f8f28aa),
	  C(7755ea02 dbccad6a), C(cb8aaf8886247a4a), C(8f 6966 ce7ea1b6e6),
	  C(3f 2863090f a45a70), C(7755ea02 dbccad6a), C(cb8aaf8886247a4a),
	  C(8f 6966 ce7ea1b6e6), C(3f 2863090f a45a70), C(1e46 d73019c9fb06),
	  C(af37f39351616f2c), C(657efdf ff20ea2ed), C(93 bdf4c58ada3ecb) },
	{ C(166 c11fbcbc89fd8), C(cce1af56c48a48aa), C(78908959 b8ede084),
	  C(19032925 ba2c951a), C(a53ed6e81b67943a), C(edc871a9e8ef4bdf),
	  C(ae66cf46a8371aba), C(19032925 ba2c951a), C(a53ed6e81b67943a),
	  C(edc871a9e8ef4bdf), C(ae66cf46a8371aba), C(a37b97790fe75861),
	  C(eda28c8622708b98), C(3f 0 a23509d3d5c9d), C(5787 b0e7976c97cf) },
	{ C(3565 bcc4ca4ce807), C(ec35bfbe575819d5), C(6 a1f690d886e0270),
	  C(1 ab8c584625f6a04), C(ccfcdafb81b572c4), C(53 b04ba39fef5af9),
	  C(64 ce81828eefeed4), C(1 ab8c584625f6a04), C(ccfcdafb81b572c4),
	  C(53 b04ba39fef5af9), C(64 ce81828eefeed4), C(131 af99997fc662c),
	  C(8 d9081192fae833c), C(2828064791 cb2eb), C(80554 d2e8294065c) },
	{ C(b7897fd2f274307d), C(6 d43a9e5dd95616d), C(31 a2218e64d8fce0),
	  C(664e581f c1cf769b), C(415110942f c97022), C(7 a5d38fee0bfa763),
	  C(dc87ddb4d7495b6c), C(664e581f c1cf769b), C(415110942f c97022),
	  C(7 a5d38fee0bfa763), C(dc87ddb4d7495b6c), C(7 c3b66372e82e64b),
	  C(1 c89c0ceeeb2dd1), C(dad76d2266214dbd), C(744783486e43 cc61) },
	{ C(aba98113ab0e4a16), C(287f 883 aede0274d), C(3ec d2a607193ba3b),
	  C(e131f6cc9e885c28), C(b399f98d827e4958), C(6eb90 c8ed6c9090c),
	  C(ec89b378612a2b86), C(e131f6cc9e885c28), C(b399f98d827e4958),
	  C(6eb90 c8ed6c9090c), C(ec89b378612a2b86), C(cfc0e126e2f860c0),
	  C(a9a8ab5dec95b1c), C(d06747f372f7c733), C(fbd643f943a026d3) },
	{ C(17f 7796e0 d4b636c), C(ddba5551d716137b), C(65f 9735375df 1 ada),
	  C(a39e946d02e14ec2), C(1 c88cc1d3822a193), C(663f 8074 a5172bb4),
	  C(8 ad2934942e4cb9c), C(a39e946d02e14ec2), C(1 c88cc1d3822a193),
	  C(663f 8074 a5172bb4), C(8 ad2934942e4cb9c), C(3 da03b033a95f16c),
	  C(54 a52f1932a1749d), C(779ee b734199bc25), C(359 ce8c8faccc57b) },
	{ C(33 c0128e62122440), C(b23a588c8c37ec2b), C(f2608199ca14c26a),
	  C(acab0139dc4f36df), C(9502 b1605ca1345a), C(32174ef1 e06a5e9c),
	  C(d824b7869258192b), C(acab0139dc4f36df), C(9502 b1605ca1345a),
	  C(32174ef1 e06a5e9c), C(d824b7869258192b), C(681 d021b52064762),
	  C(30 b6c735f80ac371), C(6 a12d8d7f78896b3), C(157111657 a972144) },
	{ C(988 bc5d290b97aef), C(6754 bb647eb47666), C(44 b5cf8b5b8106a8),
	  C(a1c5ba961937f723), C(32 d6bc7214dfcb9b), C(6863397e0f 4 c6758),
	  C(e644bcb87e3eef70), C(a1c5ba961937f723), C(32 d6bc7214dfcb9b),
	  C(6863397e0f 4 c6758), C(e644bcb87e3eef70), C(bf25ae22e7aa7c97),
	  C(f5f3177da5756312), C(56 a469cb0dbb58cd), C(5233184 bb6130470) },
	{ C(23 c8c25c2ab72381), C(d6bc672da4175fba), C(6 aef5e6eb4a4eb10),
	  C(3df 880 c945e68aed), C(5e08 a75e956d456f), C(f984f088d1a322d7),
	  C(7 d44a1b597b7a05e), C(3df 880 c945e68aed), C(5e08 a75e956d456f),
	  C(f984f088d1a322d7), C(7 d44a1b597b7a05e), C(cbd7d157b7fcb020),
	  C(2e2945 e90749c2aa), C(a86a13c934d8b1bb), C(fbe3284bb4eab95f) },
	{ C(450f e4acc4ad3749), C(3111 b29565e4f852), C(db570fc2abaf13a9),
	  C(35107 d593ba38b22), C(fd8212a125073d88), C(72805 d6e015bfacf),
	  C(6 b22ae1a29c4b853), C(35107 d593ba38b22), C(fd8212a125073d88),
	  C(72805 d6e015bfacf), C(6 b22ae1a29c4b853), C(df2401f5c3c1b633),
	  C(c72307e054c81c8f), C(3ef bfe65bd2922c0), C(b4f632e240b3190c) },
	{ C(48e1 eff032d90c50), C(dee0fe333d962b62), C(c845776990c96775),
	  C(8ea71758346 b71c9), C(d84258cab79431fd), C(af566b4975cce10a),
	  C(5 c5c7e70a91221d2), C(8ea71758346 b71c9), C(d84258cab79431fd),
	  C(af566b4975cce10a), C(5 c5c7e70a91221d2), C(c33202c7be49ea6f),
	  C(e8ade53b6cbf4caf), C(102ea04f c82ce320), C(c1f7226614715e5e) },
	{ C(c048604ba8b6c753), C(21ea6 d24b417fdb6), C(4e40 a127ad2d6834),
	  C(5234231 bf173c51), C(62319525583eaf 29), C(87632ef a9144cc04),
	  C(1749 de70c8189067), C(5234231 bf173c51), C(62319525583eaf 29),
	  C(87632ef a9144cc04), C(1749 de70c8189067), C(29672240923e8207),
	  C(11dd 247 a815f6d0d), C(8 d64e16922487ed0), C(9f a6f45d50d83627) },
	{ C(67f f1cbe469ebf84), C(3 a828ac9e5040eb0), C(85 bf1ad6b363a14b),
	  C(2f c6c0783390d035), C(ef78307f5be5524e), C(a46925b7a1a77905),
	  C(fea37470f9a51514), C(2f c6c0783390d035), C(ef78307f5be5524e),
	  C(a46925b7a1a77905), C(fea37470f9a51514), C(9 d6504cf6d3947ce),
	  C(174 cc006b8e96e7), C(d653a06d8a009836), C(7 d22b5399326a76c) },
	{ C(b45c7536bd7a5416), C(e2d17c16c4300d3c), C(b70b641138765ff5),
	  C(a5a859ab7d0ddcfc), C(8730164 a0b671151), C(af93810c10348dd0),
	  C(7256010 c74f5d573), C(a5a859ab7d0ddcfc), C(8730164 a0b671151),
	  C(af93810c10348dd0), C(7256010 c74f5d573), C(e22a335be6cd49f3),
	  C(3 bc9c8b40c9c397a), C(18 da5c08e28d3fb5), C(f58ea5a00404a5c9) },
	{ C(215 c2eaacdb48f6f), C(33 b09acf1bfa2880), C(78 c4e94ba9f28bf),
	  C(981 b7219224443d1), C(1f 476f c4344d7bba), C(abad36e07283d3a5),
	  C(831 bf61190eaaead), C(981 b7219224443d1), C(1f 476f c4344d7bba),
	  C(abad36e07283d3a5), C(831 bf61190eaaead), C(4 c90729f62432254),
	  C(2f fadc94c89f47b3), C(677e790 b43d20e9a), C(bb0a1686e7c3ae5f) },
	{ C(241 baf16d80e0fe8), C(b6b3c5b53a3ce1d), C(6 ae6b36209eecd70),
	  C(a560b6a4aa3743a4), C(b3e04f202b7a99b), C(3 b3b1573f4c97d9f),
	  C(ccad8715a65af186), C(a560b6a4aa3743a4), C(b3e04f202b7a99b),
	  C(3 b3b1573f4c97d9f), C(ccad8715a65af186), C(d0c93a838b0c37e7),
	  C(7150 aa1be7eb1aad), C(755 b1e60b84d8d), C(51916e77 b1b05ba9) },
	{ C(d10a9743b5b1c4d1), C(f16e0e147ff9ccd6), C(fbd20a91b6085ed3),
	  C(43 d309eb00b771d5), C(a6d1f26105c0f61b), C(d37ad62406e5c37e),
	  C(75 d9b28c717c8cf7), C(43 d309eb00b771d5), C(a6d1f26105c0f61b),
	  C(d37ad62406e5c37e), C(75 d9b28c717c8cf7), C(8f 5f 118 b425b57cd),
	  C(5 d806318613275f3), C(8150848 bcf89d009), C(d5531710d53e1462) },
	{ C(919ef9 e209f2edd1), C(684 c33fb726a720a), C(540353f 94e8033),
	  C(26 da1a143e7d4ec4), C(55095ea e445aacf4), C(31ef ad866d075938),
	  C(f9b580cff4445f94), C(26 da1a143e7d4ec4), C(55095ea e445aacf4),
	  C(31ef ad866d075938), C(f9b580cff4445f94), C(b1bea6b8716d9c48),
	  C(9ed2 a3df4a15dc53), C(11f 1 be58843eb8e9), C(d9899ecaaef3c77c) },
	{ C(b5f9519b6c9280b), C(7823 a2fe2e103803), C(d379a205a3bd4660),
	  C(466ec55 ee4b4302a), C(714f 1 b9985deeaf0), C(728595f 26e633 cf7),
	  C(25ec d0738e1bee2b), C(466ec55 ee4b4302a), C(714f 1 b9985deeaf0),
	  C(728595f 26e633 cf7), C(25ec d0738e1bee2b), C(db51771ad4778278),
	  C(763e5742 ac55639e), C(df040e92d38aa785), C(5df 997 d298499bf1) },
	{ C(77 a75e89679e6757), C(25 d31fee616b5dd0), C(d81f2dfd08890060),
	  C(7598df 8911dd 40 a4), C(3 b6dda517509b41b), C(7 dae29d248dfffae),
	  C(6697 c427733135f), C(7598df 8911dd 40 a4), C(3 b6dda517509b41b),
	  C(7 dae29d248dfffae), C(6697 c427733135f), C(834 d6c0444c90899),
	  C(c790675b3cd53818), C(28 bb4c996ecadf18), C(92 c648513e6e6064) },
	{ C(9 d709e1b086aabe2), C(4 d6d6a6c543e3fec), C(df73b01acd416e84),
	  C(d54f613658e35418), C(fcc88fd0567afe77), C(d18f2380980db355),
	  C(ec3896137dfbfa8b), C(d54f613658e35418), C(fcc88fd0567afe77),
	  C(d18f2380980db355), C(ec3896137dfbfa8b), C(eb48dbd9a1881600),
	  C(ca7bd7415ab43ca9), C(e6c5a362919e2351), C(2f 4e4 bd2d5267c21) },
	{ C(91 c89971b3c20a8a), C(87 b82b1d55780b5), C(bc47bb80dfdaefcd),
	  C(87e11 c0f44454863), C(2df 1 aedb5871cc4b), C(ba72fd91536382c8),
	  C(52 cebef9e6ea865d), C(87e11 c0f44454863), C(2df 1 aedb5871cc4b),
	  C(ba72fd91536382c8), C(52 cebef9e6ea865d), C(5 befc3fc66bc7fc5),
	  C(b128bbd735a89061), C(f8f500816fa012b3), C(f828626c9612f04) },
	{ C(16468 c55a1b3f2b4), C(40 b1e8d6c63c9ff4), C(143 adc6fee592576),
	  C(4 caf4deeda66a6ee), C(264720f 6f 35f 7840), C(71 c3aef9e59e4452),
	  C(97886 ca1cb073c55), C(4 caf4deeda66a6ee), C(264720f 6f 35f 7840),
	  C(71 c3aef9e59e4452), C(97886 ca1cb073c55), C(16155f ef16fc08e8),
	  C(9 d0c1d1d5254139a), C(246513 bf2ac95ee2), C(22 c8440f59925034) },
	{ C(1 a2bd6641870b0e4), C(e4126e928f4a7314), C(1e9227 d52aab00b2),
	  C(d82489179f16d4e8), C(a3c59f65e2913cc5), C(36 cbaecdc3532b3b),
	  C(f1b454616cfeca41), C(d82489179f16d4e8), C(a3c59f65e2913cc5),
	  C(36 cbaecdc3532b3b), C(f1b454616cfeca41), C(99393e31 e3eefc16),
	  C(3 ca886eac5754cdf), C(c11776fc3e4756dd), C(ca118f7059198ba) },
	{ C(1 d2f92f23d3e811a), C(e0812edbcd475412), C(92 d2d6ad29c05767),
	  C(fd7feb3d2956875e), C(d7192a886b8b01b6), C(16e71 dba55f5b85a),
	  C(93 dabd3ff22ff144), C(fd7feb3d2956875e), C(d7192a886b8b01b6),
	  C(16e71 dba55f5b85a), C(93 dabd3ff22ff144), C(14f f0a5444c272c9),
	  C(fb024d3bb8d915c2), C(1 bc3229a94cab5fe), C(6f 6f 1f b3c0dccf09) },
	{ C(a47c08255da30ca8), C(cf6962b7353f4e68), C(2808051ea18946 b1),
	  C(b5b472960ece11ec), C(13935 c99b9abbf53), C(3e80 d95687f0432c),
	  C(3516 ab536053be5), C(b5b472960ece11ec), C(13935 c99b9abbf53),
	  C(3e80 d95687f0432c), C(3516 ab536053be5), C(748 ce6a935755e20),
	  C(2961 b51d61b0448c), C(864624113 aae88d2), C(a143805366f91338) },
	{ C(efb3b0262c9cd0c), C(1273901e9 e7699b3), C(58633f 4 ad0dcd5bb),
	  C(62e33 ba258712d51), C(fa085c15d779c0e), C(2 c15d9142308c5ad),
	  C(feb517011f27be9e), C(62e33 ba258712d51), C(fa085c15d779c0e),
	  C(2 c15d9142308c5ad), C(feb517011f27be9e), C(1 b2b049793b9eedb),
	  C(d26be505fabc5a8f), C(adc483e42a5c36c5), C(c81ff37d56d3b00b) },
	{ C(5029700 a7773c3a4), C(d01231e97e300d0f), C(397 cdc80f1f0ec58),
	  C(e4041579de57c879), C(bbf513cb7bab5553), C(66 ad0373099d5fa0),
	  C(44 bb6b21b87f3407), C(e4041579de57c879), C(bbf513cb7bab5553),
	  C(66 ad0373099d5fa0), C(44 bb6b21b87f3407), C(a8108c43b4daba33),
	  C(c0b5308c311e865e), C(cdd265ada48f6fcf), C(efbc1dae0a95ac0a) },
	{ C(71 c8287225d96c9a), C(eb836740524735c4), C(4777522 d0e09846b),
	  C(16f de90d02a1343b), C(ad14e0ed6e165185), C(8df 6e0 b2f24085dd),
	  C(caa8a47292d50263), C(16f de90d02a1343b), C(ad14e0ed6e165185),
	  C(8df 6e0 b2f24085dd), C(caa8a47292d50263), C(a020413ba660359d),
	  C(9 de401413f7c8a0c), C(20 bfb965927a7c85), C(b52573e5f817ae27) },
	{ C(4e8 b9ad9347d7277), C(c0f195eeee7641cf), C(dbd810bee1ad5e50),
	  C(8459801016414808), C(6f bf75735353c2d1), C(6e69 aaf2d93ed647),
	  C(85 bb5b90167cce5e), C(8459801016414808), C(6f bf75735353c2d1),
	  C(6e69 aaf2d93ed647), C(85 bb5b90167cce5e), C(39 d79ee490d890cc),
	  C(ac9f31f7ec97deb0), C(3 bdc1cae4ed46504), C(eb5c63cfaee05622) },
	{ C(1 d5218d6ee2e52ab), C(cb25025c4daeff3b), C(aaf107566f31bf8c),
	  C(aad20d70e231582b), C(eab92d70d9a22e54), C(cc5ab266375580c0),
	  C(85091463e3630 dce), C(aad20d70e231582b), C(eab92d70d9a22e54),
	  C(cc5ab266375580c0), C(85091463e3630 dce), C(b830b617a4690089),
	  C(9 dacf13cd76f13cf), C(d47cc5224265c68f), C(f04690880202b002) },
	{ C(162360 be6c293c8b), C(ff672b4a831953c8), C(dda57487ab6f78b5),
	  C(38 a42e0db55a4275), C(585971 da56bb56d6), C(cd957009adc1482e),
	  C(d6a96021e427567d), C(38 a42e0db55a4275), C(585971 da56bb56d6),
	  C(cd957009adc1482e), C(d6a96021e427567d), C(8e2 b1a5a63cd96fe),
	  C(426ef8 ce033d722d), C(c4d1c3d8acdda5f), C(4e694 c9be38769b2) },
	{ C(31459914f 13 c8867), C(ef96f4342d3bef53), C(a4e944ee7a1762fc),
	  C(3526 d9b950a1d910), C(a58ba01135bca7c0), C(cbad32e86d60a87c),
	  C(adde1962aad3d730), C(3526 d9b950a1d910), C(a58ba01135bca7c0),
	  C(cbad32e86d60a87c), C(adde1962aad3d730), C(55f aade148929704),
	  C(bfc06376c72a2968), C(97762698 b87f84be), C(117483 d4828cbaf7) },
	{ C(6 b4e8fca9b3aecff), C(3ea0 a33def0a296c), C(901f cb5fe05516f5),
	  C(7 c909e8cd5261727), C(c5acb3d5fbdc832e), C(54eff 5 c782ad3cdd),
	  C(9 d54397f3caf5bfa), C(7 c909e8cd5261727), C(c5acb3d5fbdc832e),
	  C(54eff 5 c782ad3cdd), C(9 d54397f3caf5bfa), C(6 b53ce24c4fc3092),
	  C(2789 abfdd4c9a14d), C(94 d6a2261637276c), C(648 aa4a2a1781f25) },
	{ C(dd3271a46c7aec5d), C(fb1dcb0683d711c3), C(240332e9 ebe5da44),
	  C(479f 936 b6d496dca), C(dc2dc93d63739d4a), C(27e4151 c3870498c),
	  C(3 a3a22ba512d13ba), C(479f 936 b6d496dca), C(dc2dc93d63739d4a),
	  C(27e4151 c3870498c), C(3 a3a22ba512d13ba), C(5 da92832f96d3cde),
	  C(439 b9ad48c4e7644), C(d2939279030accd9), C(6829f 920e2950 dbe) },
	{ C(109 b226238347d6e), C(e27214c32c43b7e7), C(eb71b0afaf0163ef),
	  C(464f 1 adf4c68577), C(acf3961e1c9d897f), C(985 b01ab89b41fe1),
	  C(6972 d6237390aac0), C(464f 1 adf4c68577), C(acf3961e1c9d897f),
	  C(985 b01ab89b41fe1), C(6972 d6237390aac0), C(122 d89898e256a0e),
	  C(ac830561bd8be599), C(5744312574f bf0ad), C(7 bff7f480a924ce9) },
	{ C(cc920608aa94cce4), C(d67efe9e097bce4f), C(5687727 c2c9036a9),
	  C(8 af42343888843c), C(191433f fcbab7800), C(7eb45f c94f88a71),
	  C(31 bc5418ffb88fa8), C(8 af42343888843c), C(191433f fcbab7800),
	  C(7eb45f c94f88a71), C(31 bc5418ffb88fa8), C(4 b53a37d8f446cb7),
	  C(a6a7dfc757a60d28), C(a074be7bacbc013a), C(cc6db5f270de7adc) },
	{ C(901f f46f22283dbe), C(9dd 59794 d049a066), C(3 c7d9c3b0e77d2c6),
	  C(dc46069eec17bfdf), C(cacb63fe65d9e3e), C(362f b57287d530c6),
	  C(5854 a4fbe1762d9), C(dc46069eec17bfdf), C(cacb63fe65d9e3e),
	  C(362f b57287d530c6), C(5854 a4fbe1762d9), C(3197427495021ef c),
	  C(5f abf34386aa4205), C(ca662891de36212), C(21f 603e4 d39bca84) },
	{ C(11 b3bdda68b0725d), C(2366 bf0aa97a00bd), C(55 dc4a4f6bf47e2b),
	  C(69437142 dae5a255), C(f2980cc4816965ac), C(dbbe76ba1d9adfcf),
	  C(49 c18025c0a8b0b5), C(69437142 dae5a255), C(f2980cc4816965ac),
	  C(dbbe76ba1d9adfcf), C(49 c18025c0a8b0b5), C(fe25c147c9001731),
	  C(38 b99cad0ca30c81), C(c7ff06ac47eb950), C(a10f92885a6b3c02) },
	{ C(9f 5f 03e84 a40d232), C(1151 a9ff99da844), C(d6f2e7c559ac4657),
	  C(5e351 e20f30377bf), C(91 b3805daf12972c), C(94417f a6452a265e),
	  C(bfa301a26765a7c), C(5e351 e20f30377bf), C(91 b3805daf12972c),
	  C(94417f a6452a265e), C(bfa301a26765a7c), C(6924e2 a053297d13),
	  C(ed4a7904ed30d77e), C(d734abaad66d6eab), C(ce373e6c09e6e8a1) },
	{ C(39eef f4f60f439be), C(1f 7559 c118517c70), C(6139 d2492237a36b),
	  C(fd39b7642cecf78f), C(104f 1 af4e9201df5), C(ab1a3cc7eaeab609),
	  C(cee3363f210a3d8b), C(fd39b7642cecf78f), C(104f 1 af4e9201df5),
	  C(ab1a3cc7eaeab609), C(cee3363f210a3d8b), C(51490f 65f e56c884),
	  C(6 a8c8322cda993c), C(1f 90609 a017de1f0), C(9f 3 acea480a41edf) },
	{ C(9 b9e0126fe4b8b04), C(6 a6190d520886c41), C(69640 b27c16b3ed8),
	  C(18865f f87619fd8f), C(dec5293e665663d8), C(ea07c345872d3201),
	  C(6f ce64da038a17ab), C(18865f f87619fd8f), C(dec5293e665663d8),
	  C(ea07c345872d3201), C(6f ce64da038a17ab), C(ad48f3c826c6a83e),
	  C(70 a1ff080a4da737), C(ecdac686c7d7719), C(700338424 b657470) },
	{ C(3ec4 b8462b36df47), C(ff8de4a1cbdb7e37), C(4ed e0449884716ac),
	  C(b5f630ac75a8ce03), C(7 cf71ae74fa8566a), C(e068f2b4618df5d),
	  C(369df 952 ad3fd0b8), C(b5f630ac75a8ce03), C(7 cf71ae74fa8566a),
	  C(e068f2b4618df5d), C(369df 952 ad3fd0b8), C(5e1 ba38fea018eb6),
	  C(5ea5 edce48e3da30), C(9 b3490c941069dcb), C(e17854a44cc2fff) },
	{ C(5e3f d9298fe7009f), C(d2058a44222d5a1d), C(cc25df39bfeb005c),
	  C(1 b0118c5c60a99c7), C(6 ae919ef932301b8), C(cde25defa089c2fc),
	  C(c2a3776e3a7716c4), C(1 b0118c5c60a99c7), C(6 ae919ef932301b8),
	  C(cde25defa089c2fc), C(c2a3776e3a7716c4), C(2557 bf65fb26269e),
	  C(b2edabba58f2ae4f), C(264144e9f 0e632 cb), C(ad6481273c979566) },
	{ C(7504ec b4727b274e), C(f698cfed6bc11829), C(71 b62c425ecd348e),
	  C(2 a5e555fd35627db), C(55 d5da439c42f3b8), C(a758e451732a1c6f),
	  C(18 caa6b46664b484), C(2 a5e555fd35627db), C(55 d5da439c42f3b8),
	  C(a758e451732a1c6f), C(18 caa6b46664b484), C(6ec1 c7d1524bbad7),
	  C(1 cc3531dc422529d), C(61 a6eeb29c0e5110), C(9 cc8652016784a6a) },
	{ C(4 bdedc104d5eaed5), C(531 c4bb4fd721e5d), C(1 d860834e94a219f),
	  C(1944ec723253392 b), C(7ea6 aa6a2f278ea5), C(5f f786af8113b3d5),
	  C(194832eb9 b0b8d0f), C(1944ec723253392 b), C(7ea6 aa6a2f278ea5),
	  C(5f f786af8113b3d5), C(194832eb9 b0b8d0f), C(56 ab0396ed73fd38),
	  C(2 c88725b3dfbf89d), C(7f f57adf6275c816), C(b32f7630bcdb218) },
	{ C(da0b4a6fb26a4748), C(8 a3165320ae1af74), C(4803664ee3 d61d09),
	  C(81 d90ddff0d00fdb), C(2 c8c7ce1173b5c77), C(18 c6b6c8d3f91dfb),
	  C(415 d5cbbf7d9f717), C(81 d90ddff0d00fdb), C(2 c8c7ce1173b5c77),
	  C(18 c6b6c8d3f91dfb), C(415 d5cbbf7d9f717), C(b683e956f1eb3235),
	  C(43166dd e2b64d11f), C(f9689c90f5aad771), C(ca0ebc253c2eec38) },
	{ C(bad6dd64d1b18672), C(6 d4c4b91c68bd23f), C(d8f1507176822db7),
	  C(381068e0f 65f 708 b), C(b4f3762e451b12a6), C(6 d61ed2f6d4e741),
	  C(8 b3b9df537b91a2c), C(381068e0f 65f 708 b), C(b4f3762e451b12a6),
	  C(6 d61ed2f6d4e741), C(8 b3b9df537b91a2c), C(b0759e599a91575c),
	  C(9e7 adbcc77212239), C(cf0eba98436555fe), C(b1fcc9c42c4cd1e6) },
	{ C(98 da3fe388d5860e), C(14 a9fda8b3adb103), C(d85f5f798637994b),
	  C(6e8 e8ff107799274), C(24 a2ef180891b531), C(c0eaf33a074bcb9d),
	  C(1f a399a82974e17e), C(6e8 e8ff107799274), C(24 a2ef180891b531),
	  C(c0eaf33a074bcb9d), C(1f a399a82974e17e), C(e7c116bef933725d),
	  C(859908 c7d17b93de), C(f6cfa27113af4a72), C(edf41c5d83c721a8) },
	{ C(ef243a576431d7ac), C(92 a32619ecfae0a5), C(fb34d2c062dc803a),
	  C(f5f8b21ec30bd3a0), C(80 a442fd5c6482a8), C(4f de11e5ccde5169),
	  C(55671451f 661 a885), C(f5f8b21ec30bd3a0), C(80 a442fd5c6482a8),
	  C(4f de11e5ccde5169), C(55671451f 661 a885), C(94f 27 bc2d5d8d63e),
	  C(2156968 b87f084dc), C(b591bcae146f6fea), C(f57f4c01e41ac7fe) },
	{ C(97854 de6f22c97b6), C(1292 ac07b0f426bb), C(9 a099a28b22d3a38),
	  C(caac64f5865d87f3), C(771 b9fdbd3aa4bd2), C(88446393 c3606c2d),
	  C(bc3d3dcd5b7d6d7f), C(caac64f5865d87f3), C(771 b9fdbd3aa4bd2),
	  C(88446393 c3606c2d), C(bc3d3dcd5b7d6d7f), C(56e22512 b832d3ee),
	  C(bbc677fe5ce0b665), C(f1914b0f070e5c32), C(c10d40362472dcd1) },
	{ C(d26ce17bfc1851d), C(db30fb632c7da294), C(26 cb7b1a465400a5),
	  C(401 a0581221957e2), C(fc04e99ae3a283ce), C(fe895303ab2d1e3e),
	  C(35 ab7c498403975b), C(401 a0581221957e2), C(fc04e99ae3a283ce),
	  C(fe895303ab2d1e3e), C(35 ab7c498403975b), C(c6e4c8dc6f52fb11),
	  C(63f 0 b484c2c7502f), C(93693 da3439bdbe9), C(1264 dbaaaaf6b7f1) },
	{ C(97477 bac0ba4c7f1), C(788ef8729 dca29ac), C(63 d88e226d36132c),
	  C(330 b7e93663affbd), C(3 c59913fcf0d603f), C(e207e6572672fd0a),
	  C(8 a5dc17019c8a667), C(330 b7e93663affbd), C(3 c59913fcf0d603f),
	  C(e207e6572672fd0a), C(8 a5dc17019c8a667), C(5 c8f47ade659d40),
	  C(6e0838 e5a808e9a2), C(8 a2d9a0afcd48b19), C(d1c9d5af7b48418d) },
	{ C(f6bbcba92b11f5c8), C(72 cf221cad20f191), C(a04726593764122d),
	  C(77f bb70409d316e2), C(c864432c5208e583), C(d3f593922668c184),
	  C(23307562648 bdb54), C(77f bb70409d316e2), C(c864432c5208e583),
	  C(d3f593922668c184), C(23307562648 bdb54), C(b03e0b274f848a74),
	  C(c6121e3af71f4281), C(2e48dd 2 a16ca63ec), C(f4cd44c69ae024df) },
	{ C(1 ac8b67c1c82132), C(7536 db9591be9471), C(42f 18f be7141e565),
	  C(20085827 a39ff749), C(42e6 c504df174606), C(839 da16331fea7ac),
	  C(7f d768552b10ffc6), C(20085827 a39ff749), C(42e6 c504df174606),
	  C(839 da16331fea7ac), C(7f d768552b10ffc6), C(d1c53c90fde72640),
	  C(c61ae7cf4e266556), C(127561e440 e4c156), C(f329cae8c26af3e1) },
	{ C(9 cd716ca0eee52fa), C(67 c1076e1ef11f93), C(927342024f 36f 5 d7),
	  C(d0884af223fd056b), C(bb33aafc7b80b3e4), C(36 b722fea81a4c88),
	  C(6e72 e3022c0ed97), C(d0884af223fd056b), C(bb33aafc7b80b3e4),
	  C(36 b722fea81a4c88), C(6e72 e3022c0ed97), C(5 db446a3ba66e0ba),
	  C(2e138f b81b28ad9), C(16e8 e82995237c85), C(9730 dbfb072fbf03) },
	{ C(1909f 39123 d9ad44), C(c0bdd71c5641fdb7), C(112e5 d19abda9b14),
	  C(984 cf3f611546e28), C(d7d9c9c4e7efb5d7), C(b3152c389532b329),
	  C(1 c168b512ec5f659), C(984 cf3f611546e28), C(d7d9c9c4e7efb5d7),
	  C(b3152c389532b329), C(1 c168b512ec5f659), C(eca67cc49e26069a),
	  C(73 cb0b224d36d541), C(df8379190ae6c5fe), C(e0f6bde7c4726211) },
	{ C(1 d206f99f535efeb), C(882e15548 afc3422), C(c94f203775c8c634),
	  C(24940 a3adac420b8), C(5 adf73051c52bce0), C(1 aa5030247ed3d32),
	  C(e1ae74ab6804c08b), C(24940 a3adac420b8), C(5 adf73051c52bce0),
	  C(1 aa5030247ed3d32), C(e1ae74ab6804c08b), C(95217 bf71b0da84c),
	  C(ca9bb91c0126a36e), C(741 b9a99ea470974), C(2 adc4e34b8670f41) },
	{ C(b38c3a83042eb802), C(ea134be7c6e0c326), C(81 d396c683df4f35),
	  C(2 a55645640911e27), C(4f ac2eefbd36e26f), C(79 ad798fb4c5835c),
	  C(359 aa2faec050131), C(2 a55645640911e27), C(4f ac2eefbd36e26f),
	  C(79 ad798fb4c5835c), C(359 aa2faec050131), C(5 b802dcec21a7157),
	  C(6ec de915b75ede0a), C(f2e653587e89058b), C(a661be80528d3385) },
	{ C(488 d6b45d927161b), C(f5cac66d869a8aaf), C(c326d56c643a214e),
	  C(10 a7228693eb083e), C(1054f b19cbacf01c), C(a8f389d24587ebd8),
	  C(afcb783a39926dba), C(10 a7228693eb083e), C(1054f b19cbacf01c),
	  C(a8f389d24587ebd8), C(afcb783a39926dba), C(fe83e658532edf8f),
	  C(6f dcf97f147dc4db), C(dc5e487845abef4b), C(137693f 4ea b77e27) },
	{ C(3 d6aaa43af5d4f86), C(44 c7d370910418d8), C(d099515f7c5c4eca),
	  C(39756960441f be2f), C(fb68e5fedbe3d874), C(3f f380fbdd27b8e),
	  C(f48832fdda648998), C(39756960441f be2f), C(fb68e5fedbe3d874),
	  C(3f f380fbdd27b8e), C(f48832fdda648998), C(270dd bf2327058c9),
	  C(9ee ad83a8319d0c4), C(b4c3356e162b086d), C(88f 013588f 411 b7) },
	{ C(e5c40a6381e43845), C(312 a18e66bbceaa3), C(31365186 c2059563),
	  C(cba4c10e65410ba0), C(3 c250c8b2d72c1b6), C(177e82f 415595117),
	  C(8 c8dcfb9e73d3f6), C(cba4c10e65410ba0), C(3 c250c8b2d72c1b6),
	  C(177e82f 415595117), C(8 c8dcfb9e73d3f6), C(c017a797e49c0f7),
	  C(ea2b233b2e7d5aea), C(878 d204c55a56cb1), C(7 b1b62cc0dfdc523) },
	{ C(86f b323e5a4b710b), C(710 c1092c23a79e0), C(bd2c6d3fc949402e),
	  C(951f 2078 aa4b8099), C(e68b7fefa1cfd190), C(41525 a4990ba6d4a),
	  C(c373552ef4b51712), C(951f 2078 aa4b8099), C(e68b7fefa1cfd190),
	  C(41525 a4990ba6d4a), C(c373552ef4b51712), C(73eb44 c6122bdf5a),
	  C(58047289 a314b013), C(e31d30432521705b), C(6 cf856774873faa4) },
	{ C(7930 c09adaf6e62e), C(f230d3311593662c), C(a795b9bf6c37d211),
	  C(b57ec44bc7101b96), C(6 cb710e77767a25a), C(2f 446152 d5e3a6d0),
	  C(cd69172f94543ce3), C(b57ec44bc7101b96), C(6 cb710e77767a25a),
	  C(2f 446152 d5e3a6d0), C(cd69172f94543ce3), C(e6c2483cf425f072),
	  C(2060 d5d4379d6d5a), C(86 a3c04c2110d893), C(561 d3b8a509313c6) },
	{ C(e505e86f0eff4ecd), C(cf31e1ccb273b9e6), C(d8efb8e9d0fe575),
	  C(ed094f47671e359d), C(d9ebdb047d57611a), C(1 c620e4d301037a3),
	  C(df6f401c172f68e8), C(ed094f47671e359d), C(d9ebdb047d57611a),
	  C(1 c620e4d301037a3), C(df6f401c172f68e8), C(af0a2c7f72388ec7),
	  C(6 d4c4a087fa4564a), C(411 b30def69700a), C(67e5 c84557a47e01) },
	{ C(dedccb12011e857), C(d831f899174feda8), C(ee4bcdb5804c582a),
	  C(5 d765af4e88f3277), C(d2abe1c63ad4d103), C(342 a8ce0bc7af6e4),
	  C(31 bfda956f3e5058), C(5 d765af4e88f3277), C(d2abe1c63ad4d103),
	  C(342 a8ce0bc7af6e4), C(31 bfda956f3e5058), C(4 c7a1fec9af54bbb),
	  C(84 a88f0655899bf4), C(66f b60d0582ac601), C(be0dd1ffe967bd4a) },
	{ C(4 d679bda26f5555f), C(7 deb387eb7823c1c), C(a65ef3b4fecd6888),
	  C(a6814d3dc578b9df), C(3372111 a3292b691), C(e97589c81d92b513),
	  C(74ed d943d1b9b5bf), C(a6814d3dc578b9df), C(3372111 a3292b691),
	  C(e97589c81d92b513), C(74ed d943d1b9b5bf), C(889e38 b0af80bb7a),
	  C(a416349af3c5818b), C(f5f5bb25576221c1), C(3 be023fa6912c32e) },
	{ C(e47cd22995a75a51), C(3686350 c2569a162), C(861 afcb185b8efd9),
	  C(63672 de7951e1853), C(3 ca0c763273b99db), C(29e04f a994cccb98),
	  C(b02587d792be5ee8), C(63672 de7951e1853), C(3 ca0c763273b99db),
	  C(29e04f a994cccb98), C(b02587d792be5ee8), C(c85ada4858f7e4fc),
	  C(3f 280 ab7d5864460), C(4109822f 92f 68326), C(2 d73f61314a2f630) },
	{ C(92 ba8e12e0204f05), C(4e29321580273802), C(aa83b675ed74a851),
	  C(a16cd2e8b445a3fd), C(f0d4f9fb613c38ef), C(eee7755d444d8f2f),
	  C(b530591eb67ae30d), C(a16cd2e8b445a3fd), C(f0d4f9fb613c38ef),
	  C(eee7755d444d8f2f), C(b530591eb67ae30d), C(6f b3031a6edf8fec),
	  C(65118 d08aecf56d8), C(9 a2117bbef1faa8), C(97055 c5fd310aa93) },
	{ C(bb3a8427c64f8939), C(b5902af2ec095a04), C(89f 1 b440667b2a28),
	  C(5386ef0 b438d0330), C(d39e03c686f8a2da), C(9555249 bb9073d78),
	  C(8 c0b3623fdf0b156), C(5386ef0 b438d0330), C(d39e03c686f8a2da),
	  C(9555249 bb9073d78), C(8 c0b3623fdf0b156), C(354f c5d3a5504e5e),
	  C(b2fd7391719aa614), C(13 cd4ce3dfe27b3d), C(a2d63a85dc3cae4b) },
	{ C(998988f 7 d6dacc43), C(5f 2 b853d841152db), C(d76321badc5cb978),
	  C(e381f24ee1d9a97d), C(7 c5d95b2a3af2e08), C(ca714acc461cdc93),
	  C(1 a8ee94bc847aa3e), C(e381f24ee1d9a97d), C(7 c5d95b2a3af2e08),
	  C(ca714acc461cdc93), C(1 a8ee94bc847aa3e), C(ee59ee4c21a36f47),
	  C(d476e8bba5bf5143), C(22 a03cb5900f6ec8), C(19 d954e14f35d7a8) },
	{ C(3f 1049221dd 72 b98), C(8 d9200d7a0664c37), C(3925704 c83a5f406),
	  C(4 cbef49086e62678), C(d77dfecc2819ef19), C(c327e4deaf4c7e72),
	  C(b4d58c73a262a32d), C(4 cbef49086e62678), C(d77dfecc2819ef19),
	  C(c327e4deaf4c7e72), C(b4d58c73a262a32d), C(78 cd002324861653),
	  C(7 c3f3977576efb88), C(d1c9975fd4a4cc26), C(3e3 cbc90a9baa442) },
	{ C(419e4f f78c3e06f3), C(aa8ff514c8a141d7), C(5 bb176e21f89f10d),
	  C(becb065dc12d8b4e), C(ebee135492a2018), C(d3f07e65bcd9e13a),
	  C(85 c933e85382e9f9), C(becb065dc12d8b4e), C(ebee135492a2018),
	  C(d3f07e65bcd9e13a), C(85 c933e85382e9f9), C(2 c19ab7c419ebaca),
	  C(982375 b2999bdb46), C(652 ca1c6325d9296), C(e9c790fa8561940a) },
	{ C(9 ba090af14171317), C(b0445c5232d7be53), C(72 cc929d1577ddb8),
	  C(bc944c1b5ba2184d), C(ab3d57e5e60e9714), C(5 d8d27e7dd0a365a),
	  C(4dd 809e11740 af1a), C(bc944c1b5ba2184d), C(ab3d57e5e60e9714),
	  C(5 d8d27e7dd0a365a), C(4dd 809e11740 af1a), C(6f 42 d856faad44df),
	  C(5118 dc58d7eaf56e), C(829 bbc076a43004), C(1747f bbfaca6da98) },
	{ C(6 ad739e4ada9a340), C(2 c6c4fb3a2e9b614), C(ab58620e94ca8a77),
	  C(aaa144fbe3e6fda2), C(52 a9291d1e212bc5), C(2 b4c68291f26b570),
	  C(45351 ab332855267), C(aaa144fbe3e6fda2), C(52 a9291d1e212bc5),
	  C(2 b4c68291f26b570), C(45351 ab332855267), C(1149f 55400 bc9799),
	  C(8 c6ec1a0c617771f), C(e9966cc03f3bec05), C(3e6889140 ccd2646) },
	{ C(8ecf f07fd67e4abd), C(f1b8029b17006ece), C(21 d96d5859229a61),
	  C(b8c18d66154ac51), C(5807350371 ad7388), C(81f 783f 4f 5 ab2b8),
	  C(fa4e659f90744de7), C(b8c18d66154ac51), C(5807350371 ad7388),
	  C(81f 783f 4f 5 ab2b8), C(fa4e659f90744de7), C(809 da4baa51cad2c),
	  C(88 d5c11ff5598342), C(7 c7125b0681d67d0), C(1 b5ba6124bfed8e8) },
	{ C(497 ca8dbfee8b3a7), C(58 c708155d70e20e), C(90428 a7e349d6949),
	  C(b744f5056e74ca86), C(88 aa27b96f3d84a5), C(b4b1ee0470ac3826),
	  C(aeb46264f4e15d4f), C(b744f5056e74ca86), C(88 aa27b96f3d84a5),
	  C(b4b1ee0470ac3826), C(aeb46264f4e15d4f), C(14921 b1ee856bc55),
	  C(a341d74aaba00a02), C(4f 50 aa8e3d08a919), C(75 a148668ff3869e) },
	{ C(a929cd66daa65b0a), C(7 c0150a2d9ca564d), C(46dd ec37e2ec0a6d),
	  C(4323852 cc57e4af3), C(1f 5f 638 bbf9d2e5b), C(578f b6ac89a31d9),
	  C(7792536 d9ac4bf12), C(4323852 cc57e4af3), C(1f 5f 638 bbf9d2e5b),
	  C(578f b6ac89a31d9), C(7792536 d9ac4bf12), C(60 be62e795ef5798),
	  C(c276cc5b44febefe), C(519 ba0b9f6d1be95), C(1f dce3561ed35bb8) },
	{ C(4107 c4156bc8d4bc), C(1 cda0c6f3f0f48af), C(cf11a23299cf7181),
	  C(766 b71bff7d6f461), C(b004f2c910a6659e), C(4 c0eb3848e1a7c8),
	  C(3f 90439 d05c3563b), C(766 b71bff7d6f461), C(b004f2c910a6659e),
	  C(4 c0eb3848e1a7c8), C(3f 90439 d05c3563b), C(4 a2a013f4bc2c1d7),
	  C(888779 ab0c272548), C(ae0f8462d89a4241), C(c5c85b7c44679abd) },
	{ C(15 b38dc0e40459d1), C(344f edcfc00fff43), C(b9215c5a0fcf17df),
	  C(d178444a236c1f2d), C(5576 deee27f3f103), C(943611 bb5b1b0736),
	  C(a0fde17cb5c2316d), C(d178444a236c1f2d), C(5576 deee27f3f103),
	  C(943611 bb5b1b0736), C(a0fde17cb5c2316d), C(feaa1a047f4375f3),
	  C(5435f 313e84767 e), C(522e4333 cd0330c1), C(7e6 b609b0ea9e91f) },
	{ C(e5e5370ed3186f6c), C(4592e75 db47ea35d), C(355 d452b82250e83),
	  C(7 a265e37da616168), C(6 a1f06c34bafa27), C(fbae175e7ed22a9c),
	  C(b144e84f6f33c098), C(7 a265e37da616168), C(6 a1f06c34bafa27),
	  C(fbae175e7ed22a9c), C(b144e84f6f33c098), C(bd444561b0db41fc),
	  C(2072 c85731e7b0b0), C(ce1b1fac436b51f3), C(4f 5 d44f31a3dcdb9) },
	{ C(ea2785c8f873e28f), C(3e257272f 4464f 5f), C(9267e7 e0cc9c7fb5),
	  C(9f d4d9362494cbbc), C(e562bc615befb1b9), C(8096808 d8646cfde),
	  C(c4084a587b9776ec), C(9f d4d9362494cbbc), C(e562bc615befb1b9),
	  C(8096808 d8646cfde), C(c4084a587b9776ec), C(a9135db8a850d8e4),
	  C(fffc4f8b1a11f5af), C(c50e9173c2c6fe64), C(a32630581df4ceda) },
	{ C(e7bf98235fc8a4a8), C(4042ef2 aae400e64), C(6538 ba9ffe72dd70),
	  C(c84bb7b3881ab070), C(36f e6c51023fbda0), C(d62838514bb87ea4),
	  C(9ee b5e7934373d86), C(c84bb7b3881ab070), C(36f e6c51023fbda0),
	  C(d62838514bb87ea4), C(9ee b5e7934373d86), C(5f 8480 d0a2750a96),
	  C(40 afa38506456ad9), C(e4012b7ef2e0ddea), C(659 da200a011836b) },
	{ C(b94e261a90888396), C(1f 468 d07e853294c), C(cb2c9b863a5317b9),
	  C(4473 c8e2a3458ee0), C(258053945 ab4a39a), C(f8d745ca41962817),
	  C(7 afb6d40df9b8f71), C(4473 c8e2a3458ee0), C(258053945 ab4a39a),
	  C(f8d745ca41962817), C(7 afb6d40df9b8f71), C(9030 c2349604f677),
	  C(f544dcd593087faf), C(77 a3b0efe6345d12), C(fff4e398c05817cc) },
	{ C(4 b0226e5f5cdc9c), C(a836ae7303dc4301), C(8505e1 b628bac101),
	  C(b5f52041a698da7), C(29864874 b5f1936d), C(49 b3a0c6d78f98da),
	  C(93 a1a8c7d90de296), C(b5f52041a698da7), C(29864874 b5f1936d),
	  C(49 b3a0c6d78f98da), C(93 a1a8c7d90de296), C(ed62288423c17b7f),
	  C(685 afa2cfba09660), C(6 d9b6f59585452c6), C(e505535c4010efb9) },
	{ C(e07edbe7325c718c), C(9 db1eda964f06827), C(2f 245 ad774e4cb1b),
	  C(664ec3f ad8521859), C(406f 082 beb9ca29a), C(b6b0fb3a7981c7c8),
	  C(3eb d280b598a9721), C(664ec3f ad8521859), C(406f 082 beb9ca29a),
	  C(b6b0fb3a7981c7c8), C(3eb d280b598a9721), C(d9a6ceb072eab22a),
	  C(d5bc5df5eb2ff6f1), C(488 db3cab48daa0b), C(9916f 14f a5672f37) },
	{ C(f4b56421eae4c4e7), C(5 da0070cf40937a0), C(aca4a5e01295984a),
	  C(5414e385f 5677 a6d), C(41ef105f 8 a682a28), C(4 cd2e95ea7f5e7b0),
	  C(775 bb1e0d57053b2), C(5414e385f 5677 a6d), C(41ef105f 8 a682a28),
	  C(4 cd2e95ea7f5e7b0), C(775 bb1e0d57053b2), C(8919017805e84 b3f),
	  C(15402f 44e0 e2b259), C(483 b1309e1403c87), C(85 c7b4232d45b0d9) },
	{ C(c07fcb8ae7b4e480), C(4eb cad82e0b53976), C(8643 c63d6c78a6ce),
	  C(d4bd358fed3e6aa5), C(8 a1ba396356197d9), C(7 afc2a54733922cc),
	  C(b813bdac4c7c02ef), C(d4bd358fed3e6aa5), C(8 a1ba396356197d9),
	  C(7 afc2a54733922cc), C(b813bdac4c7c02ef), C(f6c610cf7e7c955),
	  C(dab6a53e1c0780f8), C(837 c9ffec33e5d48), C(8 cb8c20032af152d) },
	{ C(3ed ad9568a9aaab), C(23891 bbaeb3a17bc), C(4eb7238738 b0c51a),
	  C(db0c32f76f5b7fc1), C(5e41 b711f0abd1a0), C(bcb758f01ded0a11),
	  C(7 d15f7d87955e28b), C(db0c32f76f5b7fc1), C(5e41 b711f0abd1a0),
	  C(bcb758f01ded0a11), C(7 d15f7d87955e28b), C(cd2dc1f0b05939b),
	  C(9f d6d680462e4c47), C(95 d5846e993bc8ff), C(f0b3cafc2697b8a8) },
	{ C(fcabde8700de91e8), C(63784 d19c60bf366), C(8f 3 af9a056b1a1c8),
	  C(32 d3a29cf49e2dc9), C(3079 c0b0c2269bd0), C(ed76ba44f04e7b82),
	  C(6ee e76a90b83035f), C(32 d3a29cf49e2dc9), C(3079 c0b0c2269bd0),
	  C(ed76ba44f04e7b82), C(6ee e76a90b83035f), C(4 a9286f545bbc09),
	  C(bd36525be4dd1b51), C(5f 7 a9117228fdee5), C(543 c96a08f03151c) },
	{ C(362f c5ba93e8eb31), C(7549 ae99fa609d61), C(47e4 cf524e37178f),
	  C(a54eaa5d7f3a7227), C(9 d26922965d54727), C(27 d22acb31a194d4),
	  C(e9b8e68771db0da6), C(a54eaa5d7f3a7227), C(9 d26922965d54727),
	  C(27 d22acb31a194d4), C(e9b8e68771db0da6), C(16f d0e006209abe8),
	  C(81 d3f72987a6a81a), C(74e96 e4044817bc7), C(924 ca5f08572fef9) },
	{ C(e323b1c5b55a4dfb), C(719993 d7d1ad77fb), C(555 ca6c6166e989c),
	  C(ea37f61c0c2f6d53), C(9 b0c2174f14a01f5), C(7 bbe6921e26293f3),
	  C(2 ab6c72235b6c98a), C(ea37f61c0c2f6d53), C(9 b0c2174f14a01f5),
	  C(7 bbe6921e26293f3), C(2 ab6c72235b6c98a), C(2 c6e7668f37f6d23),
	  C(3e8 edb057a57c2dd), C(2595f c79768c8b34), C(ffc541f5efed9c43) },
	{ C(9461913 a153530ef), C(83f c6d9ed7d1285a), C(73df 90 bdc50807cf),
	  C(a32c192f6e3c3f66), C(8f 10077 b8a902d00), C(61 a227f2faac29b4),
	  C(1 a71466fc005a61d), C(a32c192f6e3c3f66), C(8f 10077 b8a902d00),
	  C(61 a227f2faac29b4), C(1 a71466fc005a61d), C(12545812f 3 d01a92),
	  C(aece72f823ade07d), C(52634 cdd5f9e5260), C(cb48f56805c08e98) },
	{ C(ec2332acc6df0c41), C(59f 5ee17 e20a8263), C(1087 d756afcd8e7b),
	  C(a82a7bb790678fc9), C(d197682c421e4373), C(dd78d25c7f0f935a),
	  C(9850 cb6fbfee520f), C(a82a7bb790678fc9), C(d197682c421e4373),
	  C(dd78d25c7f0f935a), C(9850 cb6fbfee520f), C(2590847398688 a46),
	  C(ad266f08713ca5fe), C(25 b978be91e830b5), C(2996 c8f2b4c8f231) },
	{ C(aae00b3a289bc82), C(4f 6 d69f5a5a5b659), C(3f f5abc145614e3),
	  C(33322363 b5f45216), C(7e83f 1f e4189e843), C(df384b2adfc35b03),
	  C(396 ce7790a5ada53), C(33322363 b5f45216), C(7e83f 1f e4189e843),
	  C(df384b2adfc35b03), C(396 ce7790a5ada53), C(c3286e44108b8d36),
	  C(6 db8716c498d703f), C(d1db09466f37f4e7), C(56 c98e7f68a41388) },
	{ C(4 c842e732fcd25f), C(e7dd7b953cf9c2b2), C(911ee248 a76ae3),
	  C(33 c6690937582317), C(fe6d61a77985d7bb), C(97 b153d04a115535),
	  C(d3fde02e42cfe6df), C(33 c6690937582317), C(fe6d61a77985d7bb),
	  C(97 b153d04a115535), C(d3fde02e42cfe6df), C(d1c7d1efa52a016),
	  C(1 d6ed137f4634c), C(1 a260ec505097081), C(8 d1e70861a1c7db6) },
	{ C(40e23 ca5817a91f3), C(353e2935809 b7ad1), C(f7820021b86391bb),
	  C(f3d41b3d4717eb83), C(2670 d457dde68842), C(19707 a6732c49278),
	  C(5 d0f05a83569ba26), C(f3d41b3d4717eb83), C(2670 d457dde68842),
	  C(19707 a6732c49278), C(5 d0f05a83569ba26), C(6f e5bc84e528816a),
	  C(94df 3 dca91a29ace), C(420196ed097 e8b6f), C(7 c52da0e1f043ad6) },
	{ C(2564527f ad710b8d), C(2 bdcca8d57f890f), C(81f 7 bfcd9ea5a532),
	  C(dd70e407984cfa80), C(66996 d6066db6e1a), C(36 a812bc418b97c9),
	  C(18ea2 c63da57f36e), C(dd70e407984cfa80), C(66996 d6066db6e1a),
	  C(36 a812bc418b97c9), C(18ea2 c63da57f36e), C(937f d7ad09be1a8f),
	  C(163 b12cab35d5d15), C(3606 c3e441335cce), C(949f 6ea5 bb241ae8) },
	{ C(6 bf70df9d15a2bf6), C(81 cad17764b8e0dd), C(58 b349a9ba22a7ef),
	  C(9432536dd 9f 65229), C(192 dc54522da3e3d), C(274 c6019e0227ca9),
	  C(160 abc932a4e4f35), C(9432536dd 9f 65229), C(192 dc54522da3e3d),
	  C(274 c6019e0227ca9), C(160 abc932a4e4f35), C(1204f 2f b5aa79dc6),
	  C(2536ed af890f0760), C(6f 2 b561f44ff46b4), C(8 c7b3e95baa8d984) },
	{ C(45e6f 446eb6 bbcf5), C(98 ab0ef06f1a7d84), C(85 ae96bacca50de6),
	  C(b9aa5bead3352801), C(8 a6d9e02a19a4229), C(c352f5b6d5ee1d9d),
	  C(ce562bdb0cfa84fb), C(b9aa5bead3352801), C(8 a6d9e02a19a4229),
	  C(c352f5b6d5ee1d9d), C(ce562bdb0cfa84fb), C(d47b768a85283981),
	  C(1f e72557be57a11b), C(95 d8afe4af087d51), C(2f 59 c4e383f30045) },
	{ C(620 d3fe4b8849c9e), C(975 a15812a429ec2), C(437 c453593dcaf13),
	  C(8 d8e7c63385df78e), C(16 d55add72a5e25e), C(aa6321421dd87eb5),
	  C(6f 27f 62e785f 0203), C(8 d8e7c63385df78e), C(16 d55add72a5e25e),
	  C(aa6321421dd87eb5), C(6f 27f 62e785f 0203), C(829030 a61078206e),
	  C(ae1f30fcfa445cc8), C(f61f21c9df4ef68d), C(1e5 b1945f858dc4c) },
	{ C(535 aa7340b3c168f), C(bed5d3c3cd87d48a), C(266 d40ae10f0cbc1),
	  C(ce218d5b44f7825a), C(2 ae0c64765800d3a), C(f22dc1ae0728fc01),
	  C(48 a171bc666d227f), C(ce218d5b44f7825a), C(2 ae0c64765800d3a),
	  C(f22dc1ae0728fc01), C(48 a171bc666d227f), C(e7367aff24203c97),
	  C(da39d2be1db3a58d), C(85 ce86523003933a), C(dfd4ef2ae83f138a) },
	{ C(dd3e761d4eada300), C(893 d7e4c3bea5bb6), C(cc6d6783bf43eea),
	  C(eb8eed7c391f0044), C(b58961c3abf80753), C(3 d75ea687191521),
	  C(389 be7bbd8e478f3), C(eb8eed7c391f0044), C(b58961c3abf80753),
	  C(3 d75ea687191521), C(389 be7bbd8e478f3), C(917070 a07441ee47),
	  C(d78efa8cd65b313), C(a8a16f4c1c08c8a1), C(b69cb8ee549eb113) },
	{ C(4 ac1902ccde06545), C(2 c44aeb0983a7a07), C(b566035215b309f9),
	  C(64 c136fe9404a7b3), C(99f 3 d8c98a399d5e), C(6319 c7cb14180185),
	  C(fbacdbd277d33f4c), C(64 c136fe9404a7b3), C(99f 3 d8c98a399d5e),
	  C(6319 c7cb14180185), C(fbacdbd277d33f4c), C(a96a5626c2adda86),
	  C(39ea72f d2ad133ed), C(b5583f2f736df73e), C(ef2c63619782b7ba) },
	{ C(aee339a23bb00a5e), C(cbb402255318f919), C(9922948e99 aa0781),
	  C(df367034233fedc4), C(dcbe14db816586e5), C(f4b1cb814adf21d3),
	  C(f4690695102fa00a), C(df367034233fedc4), C(dcbe14db816586e5),
	  C(f4b1cb814adf21d3), C(f4690695102fa00a), C(6 b4f01dd6b76dafc),
	  C(b79388676b50da5d), C(cb64f8bde5ed3393), C(9 b422781f13219d3) },
	{ C(627599e91148df 4f), C(3e2 d01e8baab062b), C(2 daab20edb245251),
	  C(9 a958bc3a895a223), C(331058dd 6 c5d2064), C(46 c4d962072094fa),
	  C(e6207c19160e58eb), C(9 a958bc3a895a223), C(331058dd 6 c5d2064),
	  C(46 c4d962072094fa), C(e6207c19160e58eb), C(5655e4 dbf7272728),
	  C(67 b217b1f56c747d), C(3 ac0be79691b9a0d), C(9 d0954dd0b57073) },
	{ C(cfb04cf00cfed6b3), C(5f e75fc559af22fa), C(c440a935d72cdc40),
	  C(3 ab0d0691b251b8b), C(47181 a443504a819), C(9 bcaf1253f99f499),
	  C(8ee002 b89c1b6b3f), C(3 ab0d0691b251b8b), C(47181 a443504a819),
	  C(9 bcaf1253f99f499), C(8ee002 b89c1b6b3f), C(55df e8eedcd1ec5e),
	  C(1 bf50f0bbad796a5), C(9044369 a042d7fd6), C(d423df3e3738ba8f) },
	{ C(942631 c47a26889), C(427962 c82d8a6e00), C(224071 a6592537ff),
	  C(d3e96f4fb479401), C(68 b3f2ec11de9368), C(cb51b01083acad4f),
	  C(500 cec4564d62aeb), C(d3e96f4fb479401), C(68 b3f2ec11de9368),
	  C(cb51b01083acad4f), C(500 cec4564d62aeb), C(4 ce547491e732887),
	  C(9423883 a9a11df4c), C(1 a0fc7a14214360), C(9e837914505 da6ed) },
	{ C(4 c9eb4e09726b47e), C(fd927483a2b38cf3), C(6 d7e56407d1ba870),
	  C(9f 5 dc7db69fa1e29), C(f42fff56934533d5), C(92 d768c230a53918),
	  C(f3360ff11642136c), C(9f 5 dc7db69fa1e29), C(f42fff56934533d5),
	  C(92 d768c230a53918), C(f3360ff11642136c), C(9e989932 eb86d1b5),
	  C(449 a77f69a8a9d65), C(efabaf8a7789ed9a), C(2798eb4 c50c826fd) },
	{ C(cf7f208ef20e887a), C(f4ce4edeadcaf1a1), C(7ee15226 eaf4a74d),
	  C(17 ab41ab2ae0705d), C(9dd 56694 aa2dcd4e), C(dd4fa2add9baced2),
	  C(7 ad99099c9e199a3), C(17 ab41ab2ae0705d), C(9dd 56694 aa2dcd4e),
	  C(dd4fa2add9baced2), C(7 ad99099c9e199a3), C(a59112144accef0e),
	  C(5838df 47e38 d251d), C(8750f e45760331e5), C(4 b2ce14732e0312a) },
	{ C(a8dc4687bcf27f4), C(c4aadd7802553f15), C(5401eb9912 be5269),
	  C(5 c2a2b5b0657a928), C(1e1968 ebb38fcb99), C(a082d0e067c4a59c),
	  C(18 b616495ad9bf5d), C(5 c2a2b5b0657a928), C(1e1968 ebb38fcb99),
	  C(a082d0e067c4a59c), C(18 b616495ad9bf5d), C(18 c5dc6c78a7f9ed),
	  C(b3cc94fe34b68aa1), C(3 b77e91292be38cc), C(61 d1786ec5097971) },
	{ C(daed638536ed19df), C(1 a762ea5d7ac6f7e), C(48 a1cc07a798b84f),
	  C(7f 15 bdaf50d550f9), C(4 c1d48aa621a037e), C(2 b1d7a389d497ee0),
	  C(81 c6775d46f4b517), C(7f 15 bdaf50d550f9), C(4 c1d48aa621a037e),
	  C(2 b1d7a389d497ee0), C(81 c6775d46f4b517), C(35296005 cbba3ebe),
	  C(db1642f825b53532), C(3e07588 a9fd829a4), C(60f 13 b5446bc7638) },
	{ C(90 a04b11ee1e4af3), C(ab09a35f8f2dff95), C(d7cbe82231ae1e83),
	  C(3262e9017 bb788c4), C(1612017731 c997bc), C(e789d66134aff5e1),
	  C(275642f d17048af1), C(3262e9017 bb788c4), C(1612017731 c997bc),
	  C(e789d66134aff5e1), C(275642f d17048af1), C(99255 b68d0b46b51),
	  C(74 a6f1ad4b2bb296), C(4164222761 af840e), C(54 d59bf6211a8fe6) },
	{ C(511f 29e1 b732617d), C(551 cb47a9a83d769), C(df6f56fbda20e7a),
	  C(f27583a930221d44), C(d7d2c46de69b2ed8), C(add24ddd2be4a850),
	  C(5 cf2f688dbb93585), C(f27583a930221d44), C(d7d2c46de69b2ed8),
	  C(add24ddd2be4a850), C(5 cf2f688dbb93585), C(a7f8e42d5dd4aa00),
	  C(72 dc11fd76b4dea9), C(8886f 194e6f 8e3f f), C(7e8 ead04a0e0b1ef) },
	{ C(95567f 03939e651f), C(62 a426f09d81d884), C(15 cb96e36a8e712c),
	  C(1 a2f43bdeaea9c28), C(bca2fd840831291f), C(83446 d4a1f7dcc1a),
	  C(449 a211df83b6187), C(1 a2f43bdeaea9c28), C(bca2fd840831291f),
	  C(83446 d4a1f7dcc1a), C(449 a211df83b6187), C(553 ce97832b2f695),
	  C(3110 a2ba303db75), C(b91d6d399a02f453), C(3 cb148561e0ef2bb) },
	{ C(248 a32ad10e76bc3), C(dac39c8b036985e9), C(79 d38c4af2958b56),
	  C(cc954b4e56275f54), C(700 cd864e04e8aaa), C(d6ba03cbff7cc34b),
	  C(da297d7891c9c046), C(cc954b4e56275f54), C(700 cd864e04e8aaa),
	  C(d6ba03cbff7cc34b), C(da297d7891c9c046), C(c05d2be8f8ee8114),
	  C(7f 4541 cbe2ec0025), C(8f 0 a7a70af6ea926), C(3837dd ce693781b5) },
	{ C(f9f05a2a892242eb), C(de00b6b2e0998460), C(f1f4bd817041497a),
	  C(3 deac49eb42a1e26), C(642f 77f 7 c57e84b7), C(2f 2 c231222651e8b),
	  C(380202ec06 bdc29e), C(3 deac49eb42a1e26), C(642f 77f 7 c57e84b7),
	  C(2f 2 c231222651e8b), C(380202ec06 bdc29e), C(59 abc4ff54765e66),
	  C(8561ea1dd dd1f742), C(9 ca1f94b0d3f3875), C(b7fa93c3a9fa4ec4) },
	{ C(3 a015cea8c3f5bdf), C(5583521 b852fc3ac), C(53 d5cd66029a1014),
	  C(ac2eeca7bb04412a), C(daba45cb16ccff2b), C(ddd90b51209e414),
	  C(d90e74ee28cb6271), C(ac2eeca7bb04412a), C(daba45cb16ccff2b),
	  C(ddd90b51209e414), C(d90e74ee28cb6271), C(117027648 ca9db68),
	  C(29 c1dba39bbcf072), C(787f 6 bb010a34cd9), C(e099f487e09b847) },
	{ C(670e43506 aa1f71b), C(1 cd7929573e54c05), C(cbb00a0aaba5f20a),
	  C(f779909e3d5688d1), C(88211 b9117678271), C(59f 44f 73759 a8bc6),
	  C(ef14f73c405123b4), C(f779909e3d5688d1), C(88211 b9117678271),
	  C(59f 44f 73759 a8bc6), C(ef14f73c405123b4), C(78775601f 11186f),
	  C(fc4641d676fbeed9), C(669 ca96b5a2ae5b), C(67 b5f0d072025f8d) },
	{ C(977 bb79b58bbd984), C(26 d45cfcfb0e9756), C(df8885db518d5f6a),
	  C(6 a1d2876488bed06), C(ae35d83c3afb5769), C(33667427 d99f9f4e),
	  C(d84c31c17495e3ba), C(6 a1d2876488bed06), C(ae35d83c3afb5769),
	  C(33667427 d99f9f4e), C(d84c31c17495e3ba), C(31357 cded7495ffc),
	  C(295e2 eefcd383a2e), C(25063ef4 a24c29ae), C(88 c694170fcbf0b7) },
	{ C(e6264fbccd93a530), C(c92f420494e99a7d), C(c14001a298cf976),
	  C(5 c8685fee2e4ce55), C(228 c49268d6a4345), C(3 b04ee2861baec6d),
	  C(7334878 a00e96e72), C(5 c8685fee2e4ce55), C(228 c49268d6a4345),
	  C(3 b04ee2861baec6d), C(7334878 a00e96e72), C(7317164 b2ce711bb),
	  C(e645447e363e8ca1), C(d326d129ad7b4e7f), C(58 b9b76d5c2eb272) },
	{ C(54e4 d0cab7ec5c27), C(31 ca61d2262a9acc), C(30 bd3a50d8082ff6),
	  C(46 b3b963bf7e2847), C(b319d04e16ad10b0), C(76 c8dd82e6f5a0eb),
	  C(2070363 cefb488bc), C(46 b3b963bf7e2847), C(b319d04e16ad10b0),
	  C(76 c8dd82e6f5a0eb), C(2070363 cefb488bc), C(6f 9 dbacb2bdc556d),
	  C(88 a5fb0b293c1e22), C(cb131d9b9abd84b7), C(21 db6f0e147a0040) },
	{ C(882 a598e98cf5416), C(36 c8dca4a80d9788), C(c386480f07591cfe),
	  C(5 b517bcf2005fd9c), C(b9b8f8e5f90e7025), C(2 a833e6199e21708),
	  C(bcb7549de5fda812), C(5 b517bcf2005fd9c), C(b9b8f8e5f90e7025),
	  C(2 a833e6199e21708), C(bcb7549de5fda812), C(44f c96a3cafa1c34),
	  C(fb7724d4899ec7c7), C(4662e3 b87df93e13), C(bcf22545acbcfd4e) },
	{ C(7 c37a5376c056d55), C(e0cce8936a06b6f6), C(d32f933fdbec4c7d),
	  C(7 ac50423e2be4703), C(546 d4b42340d6dc7), C(624f 56ee027f 12 bf),
	  C(5f 7f 65 d1e90c30f9), C(7 ac50423e2be4703), C(546 d4b42340d6dc7),
	  C(624f 56ee027f 12 bf), C(5f 7f 65 d1e90c30f9), C(d6f15c19625d2621),
	  C(c7afd12394f24b50), C(2 c6adde5d249bcd0), C(6 c857e6aa07b9fd2) },
	{ C(21 c5e9616f24be97), C(ba3536c86e4b6fe9), C(6 d3a65cfe3a9ae06),
	  C(2113903eb d760a31), C(e561f76a5eac8beb), C(86 b5b3e76392e166),
	  C(68 c8004ccc53e049), C(2113903eb d760a31), C(e561f76a5eac8beb),
	  C(86 b5b3e76392e166), C(68 c8004ccc53e049), C(b51a28fe4251dd79),
	  C(fd9c2d4d2a84c3c7), C(5 bf2ec8a470d2553), C(135 a52cdc76241c9) },
	{ C(a6eaefe74fa7d62b), C(cb34669c751b10eb), C(80 da952ad8abd5f3),
	  C(3368262 b0e172d82), C(1 d51f6c982476285), C(4497675 ac57228a9),
	  C(2 a71766a71d0b83f), C(3368262 b0e172d82), C(1 d51f6c982476285),
	  C(4497675 ac57228a9), C(2 a71766a71d0b83f), C(79 ad94d1e9c1dedd),
	  C(cbf1a1c9f23bfa40), C(3ebf 24e068 cd638b), C(be8e63472edfb462) },
	{ C(764 af88ed4b0b828), C(36946775f 20457 ce), C(d4bc88ac8281c22e),
	  C(3 b2104d68dd9ac02), C(2ec a14fcdc0892d0), C(7913 b0c09329cd47),
	  C(9373f 458938688 c8), C(3 b2104d68dd9ac02), C(2ec a14fcdc0892d0),
	  C(7913 b0c09329cd47), C(9373f 458938688 c8), C(b4448f52a5bf9425),
	  C(9f 8 c8b90b61ed532), C(78f 6774f 48e72961), C(e47c00bf9c1206f4) },
	{ C(5f 55 a694fb173ea3), C(7 db02b80ef5a918b), C(d87ff079f476ca3a),
	  C(1 d11117374e0da3), C(744 bfbde42106439), C(93 a99fab10bb1789),
	  C(246 ba292a85d8d7c), C(1 d11117374e0da3), C(744 bfbde42106439),
	  C(93 a99fab10bb1789), C(246 ba292a85d8d7c), C(e5bd7838e9edd53a),
	  C(d9c0b104c79d9019), C(ee3dcc7a8e565de5), C(619 c9e0a9cf3596d) },
	{ C(86 d086738b0a7701), C(d2402313a4280dda), C(b327aa1a25278366),
	  C(49efdd e5d1f98163), C(cbcffcee90f22824), C(951 aec1daeb79bab),
	  C(7055e2 c70d2eeb4c), C(49efdd e5d1f98163), C(cbcffcee90f22824),
	  C(951 aec1daeb79bab), C(7055e2 c70d2eeb4c), C(1f c0de9399bacb96),
	  C(dab7bbe67901959e), C(375805ec cf683ef0), C(bbb6f465c4bae04e) },
	{ C(acfc8be97115847b), C(c8f0d887bf8d9d1), C(e698fbc6d39bf837),
	  C(61f d1d6b13c1ea77), C(527ed97f f4ae24f0), C(af51a9ebb322c0),
	  C(14f 7 c25058864825), C(61f d1d6b13c1ea77), C(527ed97f f4ae24f0),
	  C(af51a9ebb322c0), C(14f 7 c25058864825), C(f40b2bbeaf9f021d),
	  C(80 d827160dfdc2d2), C(77 baea2e3650486e), C(5 de2d256740a1a97) },
	{ C(dc5ad3c016024d4), C(a0235e954da1a152), C(6 daa8a4ed194cc43),
	  C(185e650 afc8d39f8), C(adba03a4d40de998), C(9975 c776b499b26f),
	  C(9770 c59368a43a2), C(185e650 afc8d39f8), C(adba03a4d40de998),
	  C(9975 c776b499b26f), C(9770 c59368a43a2), C(d2776f0cf0e4f66c),
	  C(38ea aabfb743f7f6), C(c066f03d959b3f07), C(9 d91c2d52240d546) },
	{ C(a0e91182f03277f7), C(15 c6ebef7376556), C(516f 887657 ab5a),
	  C(f95050524c7f4b84), C(460 dcebbaaa09ae3), C(a9f7a9f0b1b2a961),
	  C(5f 8 dc5e198e34539), C(f95050524c7f4b84), C(460 dcebbaaa09ae3),
	  C(a9f7a9f0b1b2a961), C(5f 8 dc5e198e34539), C(9 c49227ffcff07cb),
	  C(a29388e9fcb794c8), C(475867910 d110cba), C(8 c9a5cee480b5bac) },
	{ C(767f 1 dbd1dba673b), C(1e466 a3848a5b01e), C(483ea def1347cd6e),
	  C(a67645c72f54fe24), C(c7a5562c69bd796b), C(e14201a35b55e4a6),
	  C(b3a6d89f19d8f774), C(a67645c72f54fe24), C(c7a5562c69bd796b),
	  C(e14201a35b55e4a6), C(b3a6d89f19d8f774), C(bb4d607ac22bebe5),
	  C(792030ed eaa924e0), C(138730 dcb60f7e32), C(699 d9dcc326c72dc) },
	{ C(a5e30221500dcd53), C(3 a1058d71c9fad93), C(510520710 c6444e8),
	  C(a6a5e60c2c1d0108), C(45 c8ea4e14bf8c6b), C(213 a7235416b86df),
	  C(c186072f80d56ad3), C(a6a5e60c2c1d0108), C(45 c8ea4e14bf8c6b),
	  C(213 a7235416b86df), C(c186072f80d56ad3), C(2e7 be098db59d832),
	  C(d5fa382f3717a0ee), C(b168b26921d243d), C(61601 a60c2addfbb) },
	{ C(ebaed82e48e18ce4), C(cfe6836b65ebe7c7), C(504 d9d388684d449),
	  C(bd9c744ee9e3308e), C(faefbb8d296b65d4), C(eba051fe2404c25f),
	  C(250 c8510b8931f87), C(bd9c744ee9e3308e), C(faefbb8d296b65d4),
	  C(eba051fe2404c25f), C(250 c8510b8931f87), C(3 c4a49150dc5676f),
	  C(6 c28793c565345c4), C(9df 6dd 8829 a6d8fb), C(760 d3a023fab72e7) },
	{ C(ffa50913362b118d), C(626 d52251a8ec3e0), C(76 ce4b9dde2e8c5e),
	  C(fc57418d92e52355), C(6 b46c559e67a063), C(3f 5 c269e10690c5c),
	  C(6870 de8d49e65349), C(fc57418d92e52355), C(6 b46c559e67a063),
	  C(3f 5 c269e10690c5c), C(6870 de8d49e65349), C(88737e5 c672de296),
	  C(ca71fca5f4c4f1ce), C(42f ca3fa7f60e031), C(4 a70246d0d4c2bd8) },
	{ C(256186 bcda057f54), C(fb059b012049fd8e), C(304e07418 b5f739b),
	  C(3e166f 9f ac2eec0b), C(82 bc11707ec4a7a4), C(e29acd3851ce36b6),
	  C(9765 ca9323d30046), C(3e166f 9f ac2eec0b), C(82 bc11707ec4a7a4),
	  C(e29acd3851ce36b6), C(9765 ca9323d30046), C(dab63e7790017f7c),
	  C(b9559988bff0f170), C(48 d9ef8aea13eee8), C(e31e47857c511ec2) },
	{ C(382 b15315e84f28b), C(f9a2578b79590b72), C(708936 af6d4450e8),
	  C(76 a9d4843df75c1c), C(2 c33447da3f2c70a), C(5e4 dcf2eaeace0d6),
	  C(2 ae1727aa7220634), C(76 a9d4843df75c1c), C(2 c33447da3f2c70a),
	  C(5e4 dcf2eaeace0d6), C(2 ae1727aa7220634), C(a122f6b52e1130ba),
	  C(a17ae9a21f345e91), C(ff67313f1d0906a9), C(bb16dc0acd6ebecc) },
	{ C(9983 a9cc5576d967), C(29e37689 a173109f), C(c526073a91f2808c),
	  C(fe9a9d4a799cf817), C(7 ca841999012c0d1), C(8 b3abfa4bd2aa28e),
	  C(4ed49274526602 eb), C(fe9a9d4a799cf817), C(7 ca841999012c0d1),
	  C(8 b3abfa4bd2aa28e), C(4ed49274526602 eb), C(40995df 99063f e23),
	  C(7f 51 b7ceded05144), C(743 c89732b265bf2), C(10 c8e1fd835713fd) },
	{ C(c2c58a843f733bdb), C(516 c47c97b4ba886), C(abc3cae0339517db),
	  C(be29af0dad5c9d27), C(70f 802599 d97fe08), C(23 af3f67d941e52b),
	  C(a031edd8b3a008fb), C(be29af0dad5c9d27), C(70f 802599 d97fe08),
	  C(23 af3f67d941e52b), C(a031edd8b3a008fb), C(43431336 b198f8fd),
	  C(7 c4b60284e1c2245), C(51ee580dd abae1b3), C(ca99bd13845d8f7f) },
	{ C(648f f27fabf93521), C(d7fba33cbc153035), C(3 dbcdcf87ad06c9e),
	  C(52dd bdc9dfd26990), C(d46784cd2aeabb28), C(bd3a15e5e4eb7177),
	  C(b5d7632e19a2cd), C(52dd bdc9dfd26990), C(d46784cd2aeabb28),
	  C(bd3a15e5e4eb7177), C(b5d7632e19a2cd), C(8007450f a355dc04),
	  C(41 ca59f64588bb5c), C(66f 2 ca6b7487499d), C(8098716530 db9bea) },
	{ C(99 be55475dcb3461), C(d94ffa462f6ba8dc), C(dbab2b456bdf13bb),
	  C(f28f496e15914b2d), C(1171 ce20f49cc87d), C(1 b5f514bc1b377a9),
	  C(8 a02cb12ec4d6397), C(f28f496e15914b2d), C(1171 ce20f49cc87d),
	  C(1 b5f514bc1b377a9), C(8 a02cb12ec4d6397), C(1 c6540740c128d79),
	  C(d085b67114969f41), C(af8c1988085306f3), C(4681f 415 d9ce8038) },
	{ C(e16fbb9303dd6d92), C(4 d92b99dd164db74), C(3f 98f 2 c9da4f5ce3),
	  C(c65b38c5a47eeed0), C(5 c5301c8ee3923a6), C(51 bf9f9eddec630e),
	  C(b1cbf1a68be455c2), C(c65b38c5a47eeed0), C(5 c5301c8ee3923a6),
	  C(51 bf9f9eddec630e), C(b1cbf1a68be455c2), C(c356f5f98499bdb8),
	  C(d897df1ad63fc1d4), C(9 bf2a3a69982e93a), C(a2380d43e271bcc8) },
	{ C(4 a57a4899834e4c0), C(836 c4df2aac32257), C(cdb66b29e3e12147),
	  C(c734232cbda1eb4c), C(30 a3cffff6b9dda0), C(d199313e17cca1ed),
	  C(594 d99e4c1360d82), C(c734232cbda1eb4c), C(30 a3cffff6b9dda0),
	  C(d199313e17cca1ed), C(594 d99e4c1360d82), C(ccc37662829a65b7),
	  C(cae30ff4d2343ce9), C(54 da907f7aade4fa), C(5 d6e4a0272958922) },
	{ C(f658958cdf49f149), C(de8e4a622b7a16b), C(a227ebf448c80415),
	  C(3 de9e38b3a369785), C(84 d160d688c573a9), C(8f 562593 add0ad54),
	  C(4446 b762cc34e6bf), C(3 de9e38b3a369785), C(84 d160d688c573a9),
	  C(8f 562593 add0ad54), C(4446 b762cc34e6bf), C(2f 795f 1594 c7d598),
	  C(29e05 bd1e0dceaff), C(a9a88f2962b49589), C(4 b9c86c141ac120b) },
	{ C(ae1befc65d3ea04d), C(cfd9bc0388c8fd00), C(522f 2e1f 6 cdb31af),
	  C(585447eb e078801a), C(14 a31676ec4a2cbd), C(b274e7e6af86a5e1),
	  C(2 d487019570bedce), C(585447eb e078801a), C(14 a31676ec4a2cbd),
	  C(b274e7e6af86a5e1), C(2 d487019570bedce), C(ea1dc9ef3c7b2fcc),
	  C(bde99d4af2f4ee8c), C(64e4 c43cd7c43442), C(9 b5262ee2eed2f99) },
	{ C(2f c8f9fc5946296d), C(6 a2b94c6765ebfa2), C(f4108b8c79662fd8),
	  C(3 a48de4a1e994623), C(6318e6 e1ff7bc092), C(84 aee2ea26a048fb),
	  C(cf3c393fdad7b184), C(3 a48de4a1e994623), C(6318e6 e1ff7bc092),
	  C(84 aee2ea26a048fb), C(cf3c393fdad7b184), C(28 b265bd8985a71e),
	  C(bd3d97dbd76d89a5), C(b04ba1623c0937d), C(b6de821229693515) },
	{ C(efdb4dc26e84dce4), C(9 ce45b6172dffee8), C(c15ad8c8bcaced19),
	  C(f10cc2bcf0475411), C(1126f 457 c160d8f5), C(34 c67f6ea249d5cc),
	  C(3 ab7633f4557083), C(f10cc2bcf0475411), C(1126f 457 c160d8f5),
	  C(34 c67f6ea249d5cc), C(3 ab7633f4557083), C(3 b2e4d8611a03bd7),
	  C(3103 d6e63d71c3c9), C(43 a56a0b93bb9d53), C(50 aa3ae25803c403) },
	{ C(e84a123b3e1b0c91), C(735 cc1d493c5e524), C(287030 af8f4ac951),
	  C(fb46abaf4713dda0), C(e8835b9a08cf8cb2), C(3 b85a40e6bee4cce),
	  C(eea02a3930757200), C(fb46abaf4713dda0), C(e8835b9a08cf8cb2),
	  C(3 b85a40e6bee4cce), C(eea02a3930757200), C(fe7057d5fb18ee87),
	  C(723 d258b36eada2a), C(67641393692 a716c), C(c8539a48dae2e539) },
	{ C(686 c22d2863c48a6), C(1ee6804 e3ddde627), C(8 d66184dd34ddac8),
	  C(35 ac1bc76c11976), C(fed58f898503280d), C(ab6fcb01c630071e),
	  C(edabf3ec7663c3c9), C(35 ac1bc76c11976), C(fed58f898503280d),
	  C(ab6fcb01c630071e), C(edabf3ec7663c3c9), C(591ec5025592 b76e),
	  C(918 a77179b072163), C(25421 d9db4c81e1a), C(96f 1 b3be51f0b548) },
	{ C(2 c5c1c9fa0ecfde0), C(266 a71b430afaec3), C(53 ab2d731bd8184a),
	  C(5722f 16 b15e7f206), C(35 bb5922c0946610), C(b8d72c08f927f2aa),
	  C(65f 2 c378cb9e8c51), C(5722f 16 b15e7f206), C(35 bb5922c0946610),
	  C(b8d72c08f927f2aa), C(65f 2 c378cb9e8c51), C(cd42fec772c2d221),
	  C(10 ccd5d7bacffdd9), C(a75ecb52192f60e2), C(a648f5fe45e5c164) },
	{ C(7 a0ac8dd441c9a9d), C(4 a4315964b7377f0), C(24092991 c8f27459),
	  C(9 c6868d561691eb6), C(78 b7016996f98828), C(651e072f 06 c9e7b7),
	  C(fed953d1251ae90), C(9 c6868d561691eb6), C(78 b7016996f98828),
	  C(651e072f 06 c9e7b7), C(fed953d1251ae90), C(7 a4d19fdd89e368c),
	  C(d8224d83b6b9a753), C(3 a93520a455ee9c9), C(159942 bea42b999c) },
	{ C(c6f9a31dfc91537c), C(b3a250ae029272f8), C(d065fc76d79ec222),
	  C(d2baa99749c71d52), C(5f 90 a2cfc2a3f637), C(79e4 aca7c8bb0998),
	  C(981633149 c85c0ba), C(d2baa99749c71d52), C(5f 90 a2cfc2a3f637),
	  C(79e4 aca7c8bb0998), C(981633149 c85c0ba), C(5 ded415df904b2ee),
	  C(d37d1fc032ebca94), C(ed5b024594967bf7), C(ed7ae636d467e961) },
	{ C(2 d12010eaf7d8d3d), C(eaec74ccd9b76590), C(541338571 d45608b),
	  C(e97454e4191065f3), C(afb357655f2a5d1c), C(521 ac1614653c130),
	  C(c8a8cac96aa7f32c), C(e97454e4191065f3), C(afb357655f2a5d1c),
	  C(521 ac1614653c130), C(c8a8cac96aa7f32c), C(196 d7f3f386dfd29),
	  C(1 dcd2da5227325cc), C(10e3 b9fa712d3405), C(fdf7864ede0856c0) },
	{ C(f46de22b2d79a5bd), C(e3e198ba766c0a29), C(828 d8c137216b797),
	  C(bafdb732c8a29420), C(2ed0 b9f4548a9ac3), C(f1ed2d5417d8d1f7),
	  C(451462f 90354 d097), C(bafdb732c8a29420), C(2ed0 b9f4548a9ac3),
	  C(f1ed2d5417d8d1f7), C(451462f 90354 d097), C(bdd091094408851a),
	  C(c4c1731c1ea46c2c), C(615 a2348d60409a8), C(fbc2f058d5539bcc) },
	{ C(2 ce2f3e89fa141fe), C(ac588fe6ab2b719), C(59 b848c80739487d),
	  C(423722957 b566d10), C(ae4be02664998dc6), C(64017 aacfa69ef80),
	  C(28076dd dbf65a40a), C(423722957 b566d10), C(ae4be02664998dc6),
	  C(64017 aacfa69ef80), C(28076dd dbf65a40a), C(873 bc41acb810f94),
	  C(ac0edafb574b7c0d), C(937 d5d5fd95330bf), C(4ea91171 e208bd7e) },
	{ C(8 aa75419d95555dd), C(bdb046419d0bf1b0), C(aadf49f217b153da),
	  C(c3cbbe7eb0f5e126), C(fd1809c329311bf6), C(9 c26cc255714d79d),
	  C(67093 aeb89f5d8c8), C(c3cbbe7eb0f5e126), C(fd1809c329311bf6),
	  C(9 c26cc255714d79d), C(67093 aeb89f5d8c8), C(265954 c61009eaf7),
	  C(a5703e8073eaf83f), C(855382 b1aed9c128), C(a6652d5a53d4a008) },
	{ C(1f bf19dd9207e6aa), C(722834f 3 c5e43cb7), C(e3c13578c5a69744),
	  C(db9120bc83472135), C(f3d9f715e669cfd5), C(63f acc852f487dda),
	  C(9f 08f d85a3a78111), C(db9120bc83472135), C(f3d9f715e669cfd5),
	  C(63f acc852f487dda), C(9f 08f d85a3a78111), C(6 c1e5c694b51b7ca),
	  C(bbceb2e47d44f6a1), C(2eb472 efe06f8330), C(1844408e2 bb87ee) },
	{ C(6f 11f 9 c1131f1182), C(6f 90740 debc7bad2), C(8 d6e4e2d46ee614b),
	  C(403e3793f 0805 ac3), C(6278 da3d8667a055), C(98ec eadb4f237978),
	  C(4 daa96284c847b0), C(403e3793f 0805 ac3), C(6278 da3d8667a055),
	  C(98ec eadb4f237978), C(4 daa96284c847b0), C(ab119ac9f803d770),
	  C(ab893fe847208376), C(f9d9968ae4472ac3), C(b149ff3b35874201) },
	{ C(92e896 d8bfdebdb5), C(2 d5c691a0acaeba7), C(377 d7f86b7cb2f8b),
	  C(b8a0738135dde772), C(57f b6c9033fc5f35), C(20e628f 266e63 e1),
	  C(1 ad6647eaaa153a3), C(b8a0738135dde772), C(57f b6c9033fc5f35),
	  C(20e628f 266e63 e1), C(1 ad6647eaaa153a3), C(10005 c85a89e601a),
	  C(cc9088ed03a78e4a), C(c8d3049b8c0d26a1), C(26e8 c0e936cf8cce) },
	{ C(369 ba54df3c534d1), C(972 c7d2be5f62834), C(112 c8d0cfcc8b1e),
	  C(bcddd22a14192678), C(446 cf170a4f05e72), C(c9e992c7a79ce219),
	  C(fa4762e60a93cf84), C(bcddd22a14192678), C(446 cf170a4f05e72),
	  C(c9e992c7a79ce219), C(fa4762e60a93cf84), C(b2e11a375a352f),
	  C(a70467d0fd624cf1), C(776 b638246febf88), C(e7d1033f7faa39b5) },
	{ C(bcc4229e083e940e), C(7 a42ebe9e8f526b5), C(bb8d1f389b0769ee),
	  C(ae6790e9fe24c57a), C(659 a16feab53eb5), C(6f d4cfade750bf16),
	  C(31 b1acd328815c81), C(ae6790e9fe24c57a), C(659 a16feab53eb5),
	  C(6f d4cfade750bf16), C(31 b1acd328815c81), C(8 a711090a6ccfd44),
	  C(363240 c31681b80e), C(ad791f19de0b07e9), C(d512217d21c7c370) },
	{ C(17 c648f416fb15ca), C(fe4d070d14d71a1d), C(ff22eac66f7eb0d3),
	  C(fa4c10f92facc6c7), C(94 cad9e4daecfd58), C(6f fcf829a275d7ef),
	  C(2 a35d2436894d549), C(fa4c10f92facc6c7), C(94 cad9e4daecfd58),
	  C(6f fcf829a275d7ef), C(2 a35d2436894d549), C(c9ea25549513f5a),
	  C(93f 7 cf06df2d0206), C(ef0da319d38fe57c), C(f715dc84df4f4a75) },
	{ C(8 b752dfa2f9fa592), C(ca95e87b662fe94d), C(34 da3aadfa49936d),
	  C(bf1696df6e61f235), C(9724f ac2c03e3859), C(d9fd1463b07a8b61),
	  C(f8e397251053d8ca), C(bf1696df6e61f235), C(9724f ac2c03e3859),
	  C(d9fd1463b07a8b61), C(f8e397251053d8ca), C(c6d26d868c9e858e),
	  C(2f 4 a1cb842ed6105), C(6 cc48927bd59d1c9), C(469e836 d0b7901e1) },
	{ C(3ed da5262a7869bf), C(a15eab8c522050c9), C(ba0853c48707207b),
	  C(4 d751c1a836dcda3), C(9747 a6e96f1dd82c), C(3 c986fc5c9dc9755),
	  C(a9d04f3a92844ecd), C(4 d751c1a836dcda3), C(9747 a6e96f1dd82c),
	  C(3 c986fc5c9dc9755), C(a9d04f3a92844ecd), C(2 da9c6cede185e36),
	  C(fae575ef03f987d6), C(b4a6a620b2bee11a), C(8 acba91c5813c424) },
	{ C(b5776f9ceaf0dba2), C(546ee e4cee927b0a), C(ce70d774c7b1cf77),
	  C(7f 707785 c2d807d7), C(1ea8247 d40cdfae9), C(4945806ea c060028),
	  C(1 a14948790321c37), C(7f 707785 c2d807d7), C(1ea8247 d40cdfae9),
	  C(4945806ea c060028), C(1 a14948790321c37), C(ba3327bf0a6ab79e),
	  C(54e2939592862 de8), C(b7d4651234fa11c7), C(d122970552454def) },
	{ C(313161f 3 ce61ec83), C(c6c5acb78303987d), C(f00761c6c6e44cee),
	  C(ea660b39d2528951), C(e84537f81a44826a), C(b850bbb69593c26d),
	  C(22499793145e1209), C(ea660b39d2528951), C(e84537f81a44826a),
	  C(b850bbb69593c26d), C(22499793145e1209), C(4 c61b993560bbd58),
	  C(636 d296abe771743), C(f1861b17b8bc3146), C(cd5fca4649d30f8a) },
	{ C(6e23080 c57f4bcb), C(5f 4 dad6078644535), C(f1591bc445804407),
	  C(46 ca76959d0d4824), C(200 b16bb4031e6a5), C(3 d0e4718ed5363d2),
	  C(4 c8cfcc96382106f), C(46 ca76959d0d4824), C(200 b16bb4031e6a5),
	  C(3 d0e4718ed5363d2), C(4 c8cfcc96382106f), C(8 d6258d795b8097b),
	  C(23 ae7cd1cab4b141), C(cbe74e8fd420afa), C(d553da4575629c63) },
	{ C(a194c120f440fd48), C(ac0d985eef446947), C(5df 9f a7d97244438),
	  C(fce2269035535eba), C(2 d9b4b2010a90960), C(2 b0952b893dd72f0),
	  C(9 a51e8462c1111de), C(fce2269035535eba), C(2 d9b4b2010a90960),
	  C(2 b0952b893dd72f0), C(9 a51e8462c1111de), C(8682 b5e0624432a4),
	  C(de8500edda7c67a9), C(4821 b171f562c5a2), C(ecb17dea1002e2df) },
	{ C(3 c78f67ee87b62fe), C(274 c83c73f20f662), C(25 a94c36d3763332),
	  C(7e053f 1 b873bed61), C(d1c343547cd9c816), C(4 deee69b90a52394),
	  C(14038f 0f 3128 ca46), C(7e053f 1 b873bed61), C(d1c343547cd9c816),
	  C(4 deee69b90a52394), C(14038f 0f 3128 ca46), C(ebbf836e38c70747),
	  C(c3c1077b9a7598d0), C(e73c720a27b07ba7), C(ec57f8a9a75af4d9) },
	{ C(b7d2aee81871e3ac), C(872 ac6546cc94ff2), C(a1b0d2f507ad2d8f),
	  C(bdd983653b339252), C(c02783d47ab815f8), C(36 c5dc27d64d776c),
	  C(5193988ee a7df808), C(bdd983653b339252), C(c02783d47ab815f8),
	  C(36 c5dc27d64d776c), C(5193988ee a7df808), C(8 d8cca9c605cdb4a),
	  C(334904f d32a1f934), C(dbfc15742057a47f), C(f3f92db42ec0cba1) },
	{ C(41ec0382933 e8f72), C(bd5e52d651bf3a41), C(cbf51a6873d4b29e),
	  C(1 c8c650bfed2c546), C(9 c9085c070350c27), C(e82305be3bded854),
	  C(cf56326bab3d685d), C(1 c8c650bfed2c546), C(9 c9085c070350c27),
	  C(e82305be3bded854), C(cf56326bab3d685d), C(f94db129adc6cecc),
	  C(1f 80871ec4 b35deb), C(c0dc1a4c74d63d0), C(d3cac509f998c174) },
	{ C(7f e4e777602797f0), C(626e62f 39f 7 c575d), C(d15d6185215fee2f),
	  C(f82ef80641514b70), C(e2702de53389d34e), C(9950592 b7f2da8d8),
	  C(d6b960bf3503f893), C(f82ef80641514b70), C(e2702de53389d34e),
	  C(9950592 b7f2da8d8), C(d6b960bf3503f893), C(95 de69e4f131a9b),
	  C(ee6f56eeff9cdefa), C(28f 4f 86 c2b856b72), C(b73d2decaac56b5b) },
	{ C(aa71127fd91bd68a), C(960f 6304500f 8069), C(5 cfa9758933beba8),
	  C(dcbbdeb1f56b0ac5), C(45164 c603d084ce4), C(85693f 4ef7 e34314),
	  C(e3a3e3a5ec1f6252), C(dcbbdeb1f56b0ac5), C(45164 c603d084ce4),
	  C(85693f 4ef7 e34314), C(e3a3e3a5ec1f6252), C(91f 4711 c59532bab),
	  C(5e5 a61d26f97200b), C(ffa65a1a41da5883), C(5f 0e712235371 eef) },
	{ C(677 b53782a8af152), C(90 d76ef694361f72), C(fa2cb9714617a9e0),
	  C(72 c8667cc1e45aa9), C(3 a0aa035bbcd1ef6), C(588e89 b034fde91b),
	  C(f62e4e1d81c1687), C(72 c8667cc1e45aa9), C(3 a0aa035bbcd1ef6),
	  C(588e89 b034fde91b), C(f62e4e1d81c1687), C(1ea81508 efa11e09),
	  C(1 cf493a4dcd49aad), C(8217 d0fbe8226130), C(607 b979c0eb297dd) },
	{ C(8f 97 bb03473c860f), C(e23e420f9a32e4a2), C(3432 c97895fea7cf),
	  C(69 cc85dac0991c6c), C(4 a6c529f94e9c36a), C(e5865f8da8c887df),
	  C(27e8 c77da38582e0), C(69 cc85dac0991c6c), C(4 a6c529f94e9c36a),
	  C(e5865f8da8c887df), C(27e8 c77da38582e0), C(8e60596 b4e327dbc),
	  C(955 cf21baa1ddb18), C(c24a8eb9360370aa), C(70 d75fd116c2cab1) },
	{ C(fe50ea9f58e4de6f), C(f0a085b814230ce7), C(89407f 0548f 90e9 d),
	  C(6 c595ea139648eba), C(efe867c726ab2974), C(26f 48ec c1c3821cf),
	  C(55 c63c1b3d0f1549), C(6 c595ea139648eba), C(efe867c726ab2974),
	  C(26f 48ec c1c3821cf), C(55 c63c1b3d0f1549), C(552e5f 78e1 d87a69),
	  C(c9bfe2747a4eedf0), C(d5230acb6ef95a1), C(1e812f 3 c0d9962bd) },
	{ C(56eb0f cb9852bd27), C(c817b9a578c7b12), C(45427842795 bfa84),
	  C(8 dccc5f52a65030c), C(f89ffa1f4fab979), C(7 d94da4a61305982),
	  C(1 ba6839d59f1a07a), C(8 dccc5f52a65030c), C(f89ffa1f4fab979),
	  C(7 d94da4a61305982), C(1 ba6839d59f1a07a), C(e0162ec1f40d583e),
	  C(6 abf0b85552c7c33), C(f14bb021a875867d), C(c12a569c8bfe3ba7) },
	{ C(6 be2903d8f07af90), C(26 aaf7b795987ae8), C(44 a19337cb53fdeb),
	  C(f0e14afc59e29a3a), C(a4d0084172a98c0d), C(275998 a345d04f0f),
	  C(db73704d81680e8d), C(f0e14afc59e29a3a), C(a4d0084172a98c0d),
	  C(275998 a345d04f0f), C(db73704d81680e8d), C(351388 cf7529b1b1),
	  C(a3155d0237571da5), C(355231 b516da2890), C(263 c5a3d498c1cc) },
	{ C(58668066 da6bfc4), C(a4ea2eb7212df3dd), C(481f 64f 7 ca220524),
	  C(11 b3b649b1cea339), C(57f 4 ad5b54d71118), C(feeb30bec803ab49),
	  C(6ed9 bcc1973d9bf9), C(11 b3b649b1cea339), C(57f 4 ad5b54d71118),
	  C(feeb30bec803ab49), C(6ed9 bcc1973d9bf9), C(bf2859d9964a70c8),
	  C(d31ab162ca25f24e), C(70349336f f55d5d5), C(9 a2fa97115ef4409) },
	{ C(2 d04d1fbab341106), C(efe0c5b2878b444c), C(882 a2a889b5e8e71),
	  C(18 cc96be09e5455), C(1 ad58fd26919e409), C(76593521 c4a0006b),
	  C(f1361f348fa7cbfb), C(18 cc96be09e5455), C(1 ad58fd26919e409),
	  C(76593521 c4a0006b), C(f1361f348fa7cbfb), C(205 bc68e660b0560),
	  C(74360e11f 9f c367e), C(a88b7b0fa86caf), C(a982d749b30d4e4c) },
	{ C(d366b37bcd83805b), C(a6d16fea50466886), C(cb76dfa8eaf74d70),
	  C(389 c44e423749aa), C(a30d802bec4e5430), C(9 ac1279f92bea800),
	  C(686ef471 c2624025), C(389 c44e423749aa), C(a30d802bec4e5430),
	  C(9 ac1279f92bea800), C(686ef471 c2624025), C(2 c21a72f8e3a3423),
	  C(df5ab83f0918646a), C(cd876e0cb4df80fa), C(5 abbb92679b3ea36) },
	{ C(bbb9bc819ab65946), C(25e0 c756c95803e2), C(82 a73a1e1cc9bf6a),
	  C(671 b931b702519a3), C(61609e7 dc0dd9488), C(9 cb329b8cab5420),
	  C(3 c64f8ea340096ca), C(671 b931b702519a3), C(61609e7 dc0dd9488),
	  C(9 cb329b8cab5420), C(3 c64f8ea340096ca), C(1690 afe3befd3afb),
	  C(4 d3c18a846602740), C(a6783133a31dd64d), C(ecf4665e6bc76729) },
	{ C(8e994 eac99bbc61), C(84 de870b6f3c114e), C(150ef c95ce7b0cd2),
	  C(4 c5d48abf41185e3), C(86049 a83c7cdcc70), C(ad828ff609277b93),
	  C(f60fe028d582ccc7), C(4 c5d48abf41185e3), C(86049 a83c7cdcc70),
	  C(ad828ff609277b93), C(f60fe028d582ccc7), C(464e0 b174da0cbd4),
	  C(eadf1df69041b06e), C(48 cb9c96a9df1cdc), C(b7e5ee62809223a1) },
	{ C(364 cabf6585e2f7d), C(3 be1cc452509807e), C(1236 ce85788680d4),
	  C(4 cea77c54fc3583a), C(9 a2a64766fd77614), C(63e6 c9254b5dc4db),
	  C(26 af12ba3bf5988e), C(4 cea77c54fc3583a), C(9 a2a64766fd77614),
	  C(63e6 c9254b5dc4db), C(26 af12ba3bf5988e), C(4 a821aca3ffa26a1),
	  C(99 aa9aacbb3d08e3), C(619 ac77b52e8a823), C(68 c745a1ce4b7adb) },
	{ C(e878e2200893d775), C(76 b1e0a25867a803), C(9 c14d6d91f5ae2c5),
	  C(ac0ffd8d64e242ed), C(e1673ee2dd997587), C(8 cdf3e9369d61003),
	  C(c37c9a5258b98eba), C(ac0ffd8d64e242ed), C(e1673ee2dd997587),
	  C(8 cdf3e9369d61003), C(c37c9a5258b98eba), C(f252b2e7b67dd012),
	  C(47f c1eb088858f28), C(59 c42e4af1353223), C(e05b6c61c19eb26e) },
	{ C(6f 6 a014b9a861926), C(269e13 a120277867), C(37f c8a181e78711b),
	  C(33dd 054 c41f3aef2), C(4f c8ab1a2ef3da7b), C(597178 c3756a06dc),
	  C(748f 8 aadc540116f), C(33dd 054 c41f3aef2), C(4f c8ab1a2ef3da7b),
	  C(597178 c3756a06dc), C(748f 8 aadc540116f), C(78e3 be34de99461e),
	  C(28 b7b60d90dddab4), C(e47475fa9327a619), C(88 b17629e6265924) },
	{ C(da52b64212e8149b), C(121e713 c1692086f), C(f3d63cfa03850a02),
	  C(f0d82bafec3c564c), C(37 dece35b549a1ce), C(5f b28f6078c4a2bd),
	  C(b69990b7d9405710), C(f0d82bafec3c564c), C(37 dece35b549a1ce),
	  C(5f b28f6078c4a2bd), C(b69990b7d9405710), C(3 af5223132071100),
	  C(56 d5bb35f3bb5d2a), C(fcad4a4d5d3a1bc7), C(f17bf3d8853724d0) },
	{ C(1100f 797 ce53a629), C(f528c6614a1a30c2), C(30e49f b56bec67fa),
	  C(f991664844003cf5), C(d54f5f6c8c7cf835), C(ca9cc4437c591ef3),
	  C(d5871c77cf8fb424), C(f991664844003cf5), C(d54f5f6c8c7cf835),
	  C(ca9cc4437c591ef3), C(d5871c77cf8fb424), C(5 cf90f1e617b750c),
	  C(1648f 825 ab986232), C(936 cf225126a60), C(90f a5311d6f2445c) },
	{ C(4f 00655 b76e9cfda), C(9 dc5c707772ed283), C(b0f885f1e01927ec),
	  C(6e4 d6843289dfb47), C(357 b41c6e5fd561f), C(491e386 bacb6df3c),
	  C(86 be1b64ecd9945c), C(6e4 d6843289dfb47), C(357 b41c6e5fd561f),
	  C(491e386 bacb6df3c), C(86 be1b64ecd9945c), C(be9547e3cfd85fae),
	  C(f9e26ac346b430a8), C(38508 b84b0e68cff), C(a28d49dbd5562703) },
	{ C(d970198b6ca854db), C(92e3 d1786ae556a0), C(99 a165d7f0d85cf1),
	  C(6548910 c5f668397), C(a5c8d20873e7de65), C(5 b7c4ecfb8e38e81),
	  C(6 aa50a5531dad63e), C(6548910 c5f668397), C(a5c8d20873e7de65),
	  C(5 b7c4ecfb8e38e81), C(6 aa50a5531dad63e), C(ab903d724449e003),
	  C(ea3cc836c28fef88), C(4 b250d6c7200949d), C(13 a110654fa916c0) },
	{ C(76 c850754f28803), C(a4bffed2982cb821), C(6710e352247 caf63),
	  C(d9cbf5b9c31d964e), C(25 c8f890178b97ae), C(e7c46064676cde9f),
	  C(d8bb5eeb49c06336), C(d9cbf5b9c31d964e), C(25 c8f890178b97ae),
	  C(e7c46064676cde9f), C(d8bb5eeb49c06336), C(962 b35ae89d5f4c1),
	  C(c49083801ac2c21), C(2 db46ddec36ff33b), C(da48992ab8da284) },
	{ C(9 c98da9763f0d691), C(f5437139a3d40401), C(6f 493 c26c42f91e2),
	  C(e857e4ab2d124d5), C(6417 bb2f363f36da), C(adc36c9c92193bb1),
	  C(d35bd456172df3df), C(e857e4ab2d124d5), C(6417 bb2f363f36da),
	  C(adc36c9c92193bb1), C(d35bd456172df3df), C(577 da94064d3a3d6),
	  C(23f 13 d7532ea496a), C(6e09392 d80b8e85b), C(2e05f f6f23663892) },
	{ C(22f 8f 6869 a5f325), C(a0e7a96180772c26), C(cb71ea6825fa3b77),
	  C(39 d3dec4e718e903), C(900 c9fbdf1ae2428), C(305301 da2584818),
	  C(c6831f674e1fdb1f), C(39 d3dec4e718e903), C(900 c9fbdf1ae2428),
	  C(305301 da2584818), C(c6831f674e1fdb1f), C(8 ad0e38ffe71babf),
	  C(554 ac85a8a837e64), C(9900 c582cf401356), C(169f 646 b01ed7762) },
	{ C(9 ae7575fc14256bb), C(ab9c5a397fabc1b3), C(1 d3f582aaa724b2e),
	  C(94412f 598ef156), C(15 bf1a588f25b327), C(5756646 bd68ce022),
	  C(f062a7d29be259a5), C(94412f 598ef156), C(15 bf1a588f25b327),
	  C(5756646 bd68ce022), C(f062a7d29be259a5), C(aa99c683cfb60b26),
	  C(9e3 b7d4b17f91273), C(301 d3f5422dd34cf), C(53 d3769127253551) },
	{ C(540040e79752 b619), C(670327e237 c88cb3), C(50962f 261 bcc31d9),
	  C(9 a8ea2b68b2847ec), C(bc24ab7d4cbbda31), C(df5aff1cd42a9b57),
	  C(db47d368295f4628), C(9 a8ea2b68b2847ec), C(bc24ab7d4cbbda31),
	  C(df5aff1cd42a9b57), C(db47d368295f4628), C(9 a66c221d1bf3f3),
	  C(7 ae74ee1281de8ee), C(a4e173e2c787621f), C(5 b51062d10ae472) },
	{ C(34 cbf85722d897b1), C(6208 cb2a0fff4eba), C(e926cbc7e86f544e),
	  C(883706 c4321efee0), C(8f d5d3d84c7827e4), C(a5c80e455a7ccaaa),
	  C(3515f 41164654591), C(883706 c4321efee0), C(8f d5d3d84c7827e4),
	  C(a5c80e455a7ccaaa), C(3515f 41164654591), C(2 c08bfc75dbfd261),
	  C(6e9 eadf14f8c965e), C(18783f 5770 cd19a3), C(a6c7f2f1aa7b59ea) },
	{ C(46 afa66366bf5989), C(aa0d424ac649008b), C(97 a9108b3cd9c5c9),
	  C(6 ca08e09227a9630), C(8 b11f73a8e5b80eb), C(2391 bb535dc7ce02),
	  C(e43e2529cf36f4b9), C(6 ca08e09227a9630), C(8 b11f73a8e5b80eb),
	  C(2391 bb535dc7ce02), C(e43e2529cf36f4b9), C(c9bd6d82b7a73d9d),
	  C(b2ed9bae888447ac), C(bd22bb13af0cd06d), C(62781441785 b355b) },
	{ C(e15074b077c6e560), C(7 c8f2173fcc34afa), C(8 aad55bc3bd38370),
	  C(d407ecdbfb7cb138), C(642442eff 44578 af), C(d3e9fdaf71a5b79e),
	  C(c87c53eda46aa860), C(d407ecdbfb7cb138), C(642442eff 44578 af),
	  C(d3e9fdaf71a5b79e), C(c87c53eda46aa860), C(8462310 a2c76ff51),
	  C(1 bc17a2e0976665e), C(6ec446 b13b4d79cf), C(388 c7a904b4264c1) },
	{ C(9740 b2b2d6d06c6), C(e738265f9de8dafc), C(fdc947c1fca8be9e),
	  C(d6936b41687c1e3d), C(a1a2deb673345994), C(91501e58 b17168bd),
	  C(b8edee2b0b708dfc), C(d6936b41687c1e3d), C(a1a2deb673345994),
	  C(91501e58 b17168bd), C(b8edee2b0b708dfc), C(ddf4b43dafd17445),
	  C(44015 d050a04ce5c), C(1019f d9ab82c4655), C(c803aea0957bcdd1) },
	{ C(f1431889f2db1bff), C(85257 aa1dc6bd0d0), C(1 abbdea0edda5be4),
	  C(775 aa89d278f26c3), C(a542d20265e3ef09), C(933 bdcac58a33090),
	  C(c43614862666ca42), C(775 aa89d278f26c3), C(a542d20265e3ef09),
	  C(933 bdcac58a33090), C(c43614862666ca42), C(4 c5e54d481a9748d),
	  C(65 ce3cd0db838b26), C(9 ccbb4005c7f09d2), C(e6dda9555dde899a) },
	{ C(e2dd273a8d28c52d), C(8 cd95915fdcfd96b), C(67 c0f5b1025f0699),
	  C(cbc94668d48df4d9), C(7e3 d656e49d632d1), C(8329e30 cac7a61d4),
	  C(38e6 cd1e2034e668), C(cbc94668d48df4d9), C(7e3 d656e49d632d1),
	  C(8329e30 cac7a61d4), C(38e6 cd1e2034e668), C(41e0 bce03ed9394b),
	  C(7 be48d0158b9834a), C(9ea8 d5d1a976b18b), C(606 c424c33617e7a) },
	{ C(e0f79029834cc6ac), C(f2b1dcb87cc5e94c), C(4210 bc221fe5e70a),
	  C(fd4a4301d4e2ac67), C(8f 84358 d25b2999b), C(6 c4b7d8a5a22ccbb),
	  C(25df 606 bb23c9d40), C(fd4a4301d4e2ac67), C(8f 84358 d25b2999b),
	  C(6 c4b7d8a5a22ccbb), C(25df 606 bb23c9d40), C(915298 b0eaadf85b),
	  C(5ec23 cc4c6a74e62), C(d640a4ff99763439), C(1603753f b34ad427) },
	{ C(9 dc0a29830bcbec1), C(ec4a01dbd52d96a0), C(cd49c657eff87b05),
	  C(ea487fe948c399e1), C(f5de9b2e59192609), C(4604 d9b3248b3a5),
	  C(1929878 a22c86a1d), C(ea487fe948c399e1), C(f5de9b2e59192609),
	  C(4604 d9b3248b3a5), C(1929878 a22c86a1d), C(3 cf6cd7c19dfa1ef),
	  C(46e404 ee4af2d726), C(613 ab0588a5527b5), C(73e39385 ced7e684) },
	{ C(d10b70dde60270a6), C(be0f3b256e23422a), C(6 c601297a3739826),
	  C(e327ffc477cd2467), C(ebebba63911f32b2), C(2 c2c5c24cf4970a2),
	  C(a3cd2c192c1b8bf), C(e327ffc477cd2467), C(ebebba63911f32b2),
	  C(2 c2c5c24cf4970a2), C(a3cd2c192c1b8bf), C(94 cb02c94aaf250b),
	  C(30 ca38d5e3dac579), C(d68598a91dc597b5), C(162 b050e8de2d92) },
	{ C(58 d2459f094d075c), C(b4df247528d23251), C(355283f 2128 a9e71),
	  C(d046198e4df506c2), C(c61bb9705786ae53), C(b360200380d10da8),
	  C(59942 bf009ee7bc), C(d046198e4df506c2), C(c61bb9705786ae53),
	  C(b360200380d10da8), C(59942 bf009ee7bc), C(95806 d027f8d245e),
	  C(32df 87487ed9 d0f4), C(e2c5bc224ce97a98), C(9 a47c1e33cfb1cc5) },
	{ C(68 c600cdd42d9f65), C(bdf0c331f039ff25), C(1354 ac1d98944023),
	  C(b5cdfc0b06fd1bd9), C(71f 0 ce33b183efab), C(d8ae4f9d4b949755),
	  C(877 da19d6424f6b3), C(b5cdfc0b06fd1bd9), C(71f 0 ce33b183efab),
	  C(d8ae4f9d4b949755), C(877 da19d6424f6b3), C(f7cc5cbf76bc6006),
	  C(c93078f44b98efdb), C(3 d482142c727e8bc), C(8e23f 92e0616 d711) },
	{ C(9f c0bd876cb975da), C(80f 41015045 d1ade), C(5 cbf601fc55c809a),
	  C(7 d9c567075001705), C(a2fafeed0df46d5d), C(a70b82990031da8f),
	  C(8611 c76abf697e56), C(7 d9c567075001705), C(a2fafeed0df46d5d),
	  C(a70b82990031da8f), C(8611 c76abf697e56), C(806911617e1 ee53),
	  C(1 ce82ae909fba503), C(52df 85f ea9e404bd), C(dbd184e5d9a11a3e) },
	{ C(7 b3e8c267146c361), C(c6ad095af345b726), C(af702ddc731948bd),
	  C(7 ca4c883bded44b5), C(c90beb31ee9b699a), C(2 cdb4aba3d59b8a3),
	  C(df0d4fa685e938f0), C(7 ca4c883bded44b5), C(c90beb31ee9b699a),
	  C(2 cdb4aba3d59b8a3), C(df0d4fa685e938f0), C(cc0e568e91aaa382),
	  C(70 ca583a464dbea), C(b7a5859b44710e1a), C(ad141467fdf9a83a) },
	{ C(6 c49c6b3c9dd340f), C(897 c41d89af37bd1), C(52df 69e0 e2c68a8d),
	  C(eec4be1f65531a50), C(bf23d928f20f1b50), C(c642009b9c593940),
	  C(c5e59e6ca9e96f85), C(eec4be1f65531a50), C(bf23d928f20f1b50),
	  C(c642009b9c593940), C(c5e59e6ca9e96f85), C(7f bd53343e7da499),
	  C(dd87e7b88afbd251), C(92696e7683 b9f322), C(60f f51ef02c24652) },
	{ C(47324327 a4cf1732), C(6044753 d211e1dd5), C(1ec ae46d75192d3b),
	  C(b6d6315a902807e3), C(ccc8312c1b488e5d), C(b933a7b48a338ec),
	  C(9 d6753cd83422074), C(b6d6315a902807e3), C(ccc8312c1b488e5d),
	  C(b933a7b48a338ec), C(9 d6753cd83422074), C(5714 bd5c0efdc7a8),
	  C(221585e2 c88068ca), C(303342 b25678904), C(8 c174a03e69a76e) },
	{ C(1e984 ef53c5f6aae), C(99ea10 dac804298b), C(a3f8c241100fb14d),
	  C(259eb3 c63a9c9be6), C(f8991532947c7037), C(a16d20b3fc29cfee),
	  C(493 c2e91a775af8c), C(259eb3 c63a9c9be6), C(f8991532947c7037),
	  C(a16d20b3fc29cfee), C(493 c2e91a775af8c), C(275f ccf4acb08abc),
	  C(d13fb6ea3eeaf070), C(505283e5 b702b9ea), C(64 c092f9f8df1901) },
	{ C(b88f5c9b8b854cc6), C(54f c5d39825b446), C(a12fc1546eac665d),
	  C(ab90eb7fa58b280c), C(dda26598356aa599), C(64191 d63f2586e52),
	  C(cada0075c34e8b02), C(ab90eb7fa58b280c), C(dda26598356aa599),
	  C(64191 d63f2586e52), C(cada0075c34e8b02), C(e7de6532b691d87c),
	  C(a28fec86e368624), C(796 c280eebd0241a), C(acfcecb641fdbeee) },
	{ C(9f cb3fdb09e7a63a), C(7 a115c9ded150112), C(e9ba629108852f37),
	  C(9 b03c7c218c192a), C(93 c1dd563f46308e), C(f9553625917ea800),
	  C(e0a52f8a5024c59), C(9 b03c7c218c192a), C(93 c1dd563f46308e),
	  C(f9553625917ea800), C(e0a52f8a5024c59), C(2 bb3a9e8b053e490),
	  C(8 b97936723cd8ff6), C(bf3f835246d02722), C(c8e033da88ecd724) },
	{ C(d58438d62089243), C(d8c19375b228e9d3), C(13042546ed96 e790),
	  C(4 a42ef343514138c), C(549e62449 e225cf1), C(dd8260e2808f68e8),
	  C(69580f c81fcf281b), C(4 a42ef343514138c), C(549e62449 e225cf1),
	  C(dd8260e2808f68e8), C(69580f c81fcf281b), C(fc0e30d682e87289),
	  C(f44b784248d6107b), C(df25119527fdf209), C(cc265612588171a8) },
	{ C(7ea73 b6b74c8cd0b), C(e07188dd9b5bf3ca), C(6ef62f f2dd008ed4),
	  C(acd94b3038342152), C(1 b0ed99c9b7ba297), C(b794a93f4c895939),
	  C(97 a60cd93021206d), C(acd94b3038342152), C(1 b0ed99c9b7ba297),
	  C(b794a93f4c895939), C(97 a60cd93021206d), C(9e0 c0e6da5001b07),
	  C(5f 5 b817de5d2a391), C(35 b8a8702acdd533), C(3 bbcfef344f455) },
	{ C(e42ffdf6278bb21), C(59df 3e5 ca582ff9d), C(f3108785599dbde9),
	  C(f78e8a2d4aba6a1d), C(700473f b0d8380fc), C(d0a0d68061ac74b2),
	  C(11650612f a426e5a), C(f78e8a2d4aba6a1d), C(700473f b0d8380fc),
	  C(d0a0d68061ac74b2), C(11650612f a426e5a), C(e39ceb5b2955710c),
	  C(f559ff201f8cebaa), C(1f bc182809e829a0), C(295 c7fc82fa6fb5b) },
	{ C(9 ad37fcd49fe4aa0), C(76 d40da71930f708), C(bea08b630f731623),
	  C(797292108901 a81f), C(3 b94127b18fae49c), C(688247179f 144f 1 b),
	  C(48 a507a1625d13d7), C(797292108901 a81f), C(3 b94127b18fae49c),
	  C(688247179f 144f 1 b), C(48 a507a1625d13d7), C(452322 aaad817005),
	  C(51 d730d973e13d44), C(c883eb30176652ea), C(8 d338fd678b2404d) },
	{ C(27 b7ff391136696e), C(60 db94a18593438c), C(b5e46d79c4dafbad),
	  C(ad56fd25a6f15289), C(68 a0ec7c0179df80), C(a0aacfc36620957),
	  C(87 a0762a09e2e1c1), C(ad56fd25a6f15289), C(68 a0ec7c0179df80),
	  C(a0aacfc36620957), C(87 a0762a09e2e1c1), C(d50ace99460f0be3),
	  C(7f 1f e5653ae0d999), C(3870899 d9d6c22c), C(df5f952dd90d5a09) },
	{ C(76 bd077e42692ddf), C(c14b60958c2c7a85), C(fd9f3b0b3b1e2738),
	  C(273 d2c51a8e65e71), C(ac531423f670bf34), C(7f 40 c6bfb8c5758a),
	  C(5f de65b433a10b02), C(273 d2c51a8e65e71), C(ac531423f670bf34),
	  C(7f 40 c6bfb8c5758a), C(5f de65b433a10b02), C(dbda6c4252b0a75c),
	  C(5 d4cfd8f937b23d9), C(3895f 478e1 c29c9d), C(e3e7c1fd1199aec6) },
	{ C(81 c672225442e053), C(927 c3f6c8964050e), C(cb59f8f2bb36fac5),
	  C(298f 3583326f d942), C(b85602a9a2e2f97c), C(65 c849bfa3191459),
	  C(bf21329dfb496c0d), C(298f 3583326f d942), C(b85602a9a2e2f97c),
	  C(65 c849bfa3191459), C(bf21329dfb496c0d), C(ea7b7b44c596aa18),
	  C(c18bfb6e9a36d59c), C(1 b55f03e8a38cc0a), C(b6a94cd47bbf847f) },
	{ C(37 b9e308747448ca), C(513f 39f 5545 b1bd), C(145 b32114ca00f9c),
	  C(cce24b9910eb0489), C(af4ac64668ac57d9), C(ea0e44c13a9a5d5e),
	  C(b224fb0c680455f4), C(cce24b9910eb0489), C(af4ac64668ac57d9),
	  C(ea0e44c13a9a5d5e), C(b224fb0c680455f4), C(a7714bbba8699be7),
	  C(fecad6e0e0092204), C(c1ce8bd5ac247eb4), C(3993 aef5c07cdca2) },
	{ C(dab71695950a51d4), C(9e98 e4dfa07566fe), C(fab3587513b84ec0),
	  C(2409f 60f 0854f 305), C(b17f6e6c8ff1894c), C(62f a048551dc7ad6),
	  C(d99f4fe2799bad72), C(2409f 60f 0854f 305), C(b17f6e6c8ff1894c),
	  C(62f a048551dc7ad6), C(d99f4fe2799bad72), C(4 a38e7f2f4a669d3),
	  C(53173510 ca91f0e3), C(cc9096c0df860b0), C(52ed637026 a4a0d5) },
	{ C(28630288285 c747b), C(a165a5bf51aaec95), C(927 d211f27370016),
	  C(727 c782893d30c22), C(742706852989 c247), C(c546494c3bb5e7e2),
	  C(1f b2a5d1570f5dc0), C(727 c782893d30c22), C(742706852989 c247),
	  C(c546494c3bb5e7e2), C(1f b2a5d1570f5dc0), C(71e498804df 91 b76),
	  C(4 a6a5aa6f7e5621), C(871 a63730d13a544), C(63f 77 c8f371cc2f8) },
	{ C(4 b591ad5160b6c1b), C(e8f85ddd5a1143f7), C(377e18171476 d64),
	  C(829481773 cce2cb1), C(c9d9fb4e25e4d243), C(c1fff894f0cf713b),
	  C(69ed d73ec20984b0), C(829481773 cce2cb1), C(c9d9fb4e25e4d243),
	  C(c1fff894f0cf713b), C(69ed d73ec20984b0), C(7f b1132262925f4a),
	  C(a292e214fe56794f), C(915 bfee68e16f46f), C(98 bcc857bb6d31e7) },
	{ C(7e02f 7 a5a97dd3df), C(9724 a88ac8c30809), C(d8dee12589eeaf36),
	  C(c61f8fa31ad1885b), C(3e3744 e04485ff9a), C(939335 b37f34c7a2),
	  C(faa5de308dbbbc39), C(c61f8fa31ad1885b), C(3e3744 e04485ff9a),
	  C(939335 b37f34c7a2), C(faa5de308dbbbc39), C(f5996b1be7837a75),
	  C(4f cb12d267f5af4f), C(39 be67b8cd132169), C(5 c39e3819198b8a1) },
	{ C(ff66660873521fb2), C(d82841f7e714ce03), C(c830d273f005e378),
	  C(66990 c8c54782228), C(4f 28 bea83dda97c), C(6 a24c64698688de0),
	  C(69721141111 da99b), C(66990 c8c54782228), C(4f 28 bea83dda97c),
	  C(6 a24c64698688de0), C(69721141111 da99b), C(d5c771fade83931b),
	  C(8094ed75 e6feb396), C(7 a79d4de8efd1a2c), C(5f 9e50167693 e363) },
	{ C(ef3c4dd60fa37412), C(e8d2898c86d11327), C(8 c883d860aafacfe),
	  C(a4ace72ba19d6de5), C(4 cae26627dfc5511), C(38e496 de9f677b05),
	  C(558770996e1906 d6), C(a4ace72ba19d6de5), C(4 cae26627dfc5511),
	  C(38e496 de9f677b05), C(558770996e1906 d6), C(40df 30e332 ceca69),
	  C(8f 106 cbd94166c42), C(332 b6ab4f4c1014e), C(7 c0bc3092ad850e5) },
	{ C(a7b07bcb1a1333ba), C(9 d007956720914c3), C(4751f 60ef2 b15545),
	  C(77 ac4dcee10c9023), C(e90235108fa20e56), C(1 d3ea38535215800),
	  C(5ed1 ccfff26bc64), C(77 ac4dcee10c9023), C(e90235108fa20e56),
	  C(1 d3ea38535215800), C(5ed1 ccfff26bc64), C(789 a1c352bf5c61e),
	  C(860 a119056da8252), C(a6c268a238699086), C(4 d70f5cccf4ef2eb) },
	{ C(89858f c94ee25469), C(f72193b78aeaa896), C(7 dba382760727c27),
	  C(846 b72f372f1685a), C(f708db2fead5433c), C(c04e121770ee5dc),
	  C(4619793 b67d0daa4), C(846 b72f372f1685a), C(f708db2fead5433c),
	  C(c04e121770ee5dc), C(4619793 b67d0daa4), C(79f 80506f 152285f),
	  C(5300074926f ccd56), C(7f bbff6cc418fce6), C(b908f77c676b32e4) },
	{ C(e6344d83aafdca2e), C(6e147816 e6ebf87), C(8508 c38680732caf),
	  C(f4ce36d3a375c981), C(9 d67e5572f8d7bf4), C(900 d63d9ec79e477),
	  C(5251 c85ab52839a3), C(f4ce36d3a375c981), C(9 d67e5572f8d7bf4),
	  C(900 d63d9ec79e477), C(5251 c85ab52839a3), C(92ec4 b3952e38027),
	  C(40 b2dc421a518cbf), C(661ea97 b2331a070), C(8 d428a4a9485179b) },
	{ C(3dd bb400198d3d4d), C(fe73de3ada21af5c), C(cd7df833dacd8da3),
	  C(162 be779eea87bf8), C(7 d62d36edf759e6d), C(dc20f528362e37b2),
	  C(1 a902edfe4a5824e), C(162 be779eea87bf8), C(7 d62d36edf759e6d),
	  C(dc20f528362e37b2), C(1 a902edfe4a5824e), C(e6a258d30fa817ba),
	  C(c5d73adf6fb196fd), C(475 b7a6286a207fb), C(d35f96363e8eba95) },
	{ C(79 d4c20cf83a7732), C(651ea0 a6ab059bcd), C(94631144f 363 cdef),
	  C(894 a0ee0c1f87a22), C(4e682573f 8 b38f25), C(89803f c082816289),
	  C(71613963 a02d90e1), C(894 a0ee0c1f87a22), C(4e682573f 8 b38f25),
	  C(89803f c082816289), C(71613963 a02d90e1), C(4 c6cc0e5a737c910),
	  C(a3765b5da16bccd9), C(8 bf483c4d735ec96), C(7f d7c8ba1934afec) },
	{ C(5 aaf0d7b669173b5), C(19661 ca108694547), C(5 d03d681639d71fe),
	  C(7 c422f4a12fd1a66), C(aa561203e7413665), C(e99d8d202a04d573),
	  C(6090357ec6f 1f 1), C(7 c422f4a12fd1a66), C(aa561203e7413665),
	  C(e99d8d202a04d573), C(6090357ec6f 1f 1), C(dbfe89f01f0162e),
	  C(49 aa89da4f1e389b), C(7119 a6f4514efb22), C(56593f 6 b4e7318d9) },
	{ C(35 d6cc883840170c), C(444694 c4f8928732), C(98500f 14 b8741c6),
	  C(5021 ac9480077dd), C(44 c2ebc11cfb9837), C(e5d310c4b5c1d9fd),
	  C(a577102c33ac773c), C(5021 ac9480077dd), C(44 c2ebc11cfb9837),
	  C(e5d310c4b5c1d9fd), C(a577102c33ac773c), C(a00d2efd2effa3cf),
	  C(c2c33ffcda749df6), C(d172099d3b6f2986), C(f308fe33fcd23338) },
	{ C(b07eead7a57ff2fe), C(c1ffe295ca7dbf47), C(ef137b125cfa8851),
	  C(8f 8ee c5cde7a490a), C(79916 d20a405760b), C(3 c30188c6d38c43c),
	  C(b17e3c3ff7685e8d), C(8f 8ee c5cde7a490a), C(79916 d20a405760b),
	  C(3 c30188c6d38c43c), C(b17e3c3ff7685e8d), C(ac8aa3cd0790c4c9),
	  C(78 ca60d8bf10f670), C(26f 522 be4fbc1184), C(55 bc7688083326d4) },
	{ C(20f ba36c76380b18), C(95 c39353c2a3477d), C(4f 362902 cf9117ad),
	  C(89816ec851 e3f405), C(65258396f 932858 d), C(b7dcaf3cc57a0017),
	  C(b368f482afc90506), C(89816ec851 e3f405), C(65258396f 932858 d),
	  C(b7dcaf3cc57a0017), C(b368f482afc90506), C(88f 08 c74465015f1),
	  C(94eb af209d59099d), C(c1b7ff7304b0a87), C(56 bf8235257d4435) },
	{ C(c7e9e0c45afeab41), C(999 d95f41d9ee841), C(55ef15 ac11ea010),
	  C(cc951b8eab5885d), C(956 c702c88ac056b), C(de355f324a37e3c0),
	  C(ed09057eb60bd463), C(cc951b8eab5885d), C(956 c702c88ac056b),
	  C(de355f324a37e3c0), C(ed09057eb60bd463), C(1f 44 b6d04a43d088),
	  C(53631822 a26ba96d), C(90305f c2d21f8d28), C(60693 a9a6093351a) },
	{ C(69 a8e59c1577145d), C(cb04a6e309ebc626), C(9 b3326a5b250e9b1),
	  C(d805f665265fd867), C(82 b2b019652c19c6), C(f0df7738353c82a6),
	  C(6 a9acf124383ca5f), C(d805f665265fd867), C(82 b2b019652c19c6),
	  C(f0df7738353c82a6), C(6 a9acf124383ca5f), C(6838374508 a7a99f),
	  C(7 b6719db8d3e40af), C(1 a22666cf0dcb7cf), C(989 a9cf7f46b434d) },
	{ C(6638191337226509), C(42 b55e08e4894870), C(a7696f5fbd51878e),
	  C(433 bbdd27481d85d), C(ee32136b5a47bbec), C(769 a77f346d82f4e),
	  C(38 b91b1cb7e34be), C(433 bbdd27481d85d), C(ee32136b5a47bbec),
	  C(769 a77f346d82f4e), C(38 b91b1cb7e34be), C(cb10fd95c0e43875),
	  C(ce9744efd6f11427), C(946 b32bddada6a13), C(35 d544690b99e3b6) },
	{ C(c44e8c33ff6c5e13), C(1f 128 a22aab3007f), C(6 a8b41bf04cd593),
	  C(1 b9b0deaf126522a), C(cc51d382baedc2eb), C(8df 8831 bb2e75daa),
	  C(de4e7a4b5de99588), C(1 b9b0deaf126522a), C(cc51d382baedc2eb),
	  C(8df 8831 bb2e75daa), C(de4e7a4b5de99588), C(55 a2707103a9f968),
	  C(e0063f4e1649702d), C(7e82f 5 b440e74043), C(649 b44a27f00219d) },
	{ C(68125009158 bded7), C(563 a9a62753fc088), C(b97a9873a352cf6a),
	  C(237 d1de15ae56127), C(b96445f758ba57d), C(b842628a9f9938eb),
	  C(70313 d232dc2cd0d), C(237 d1de15ae56127), C(b96445f758ba57d),
	  C(b842628a9f9938eb), C(70313 d232dc2cd0d), C(8 bfe1f78cb40ad5b),
	  C(a5bde811d49f56e1), C(1 acd0cf913ded507), C(820 b3049fa5e6786) },
	{ C(e0dd644db35a62d6), C(292889772752 ab42), C(b80433749dbb8793),
	  C(7032f e67035f95db), C(d8076d1fda17eb8d), C(115 ca1775560f946),
	  C(92 da1e16f396bf61), C(7032f e67035f95db), C(d8076d1fda17eb8d),
	  C(115 ca1775560f946), C(92 da1e16f396bf61), C(17 c8bc7f6d23a639),
	  C(fb28a2afa4d562a9), C(6 c59c95fa2450d5f), C(fe0d41d5ebfbce2a) },
	{ C(21 ce9eab220aaf87), C(27 d20caec922d708), C(610 c51f976cb1d30),
	  C(6052f 97 a1e02d2ba), C(836ee a7ce63dea17), C(e1f8efb81b443b45),
	  C(ddbdbbe717570246), C(6052f 97 a1e02d2ba), C(836ee a7ce63dea17),
	  C(e1f8efb81b443b45), C(ddbdbbe717570246), C(69551045 b0e56f60),
	  C(625 a435960ba7466), C(9 cdb004e8b11405c), C(d6284db99a3b16af) },
	{ C(83 b54046fdca7c1e), C(e3709e9153c01626), C(f306b5edc2682490),
	  C(88f 14 b0b554fba02), C(a0ec13fac0a24d0), C(f468ebbc03b05f47),
	  C(a9cc417c8dad17f0), C(88f 14 b0b554fba02), C(a0ec13fac0a24d0),
	  C(f468ebbc03b05f47), C(a9cc417c8dad17f0), C(4 c1ababa96d42275),
	  C(c112895a2b751f17), C(5dd 7 d9fa55927aa9), C(ca09db548d91cd46) },
	{ C(dd3b2ce7dabb22fb), C(64888 c62a5cb46ee), C(f004e8b4b2a97362),
	  C(31831 cf3efc20c84), C(901 ba53808e677ae), C(4 b36895c097d0683),
	  C(7 d93ad993f9179aa), C(31831 cf3efc20c84), C(901 ba53808e677ae),
	  C(4 b36895c097d0683), C(7 d93ad993f9179aa), C(a4c5ea29ae78ba6b),
	  C(9 cf637af6d607193), C(5731 bd261d5b3adc), C(d59a9e4f796984f3) },
	{ C(9ee08f c7a86b0ea6), C(5 c8d17dff5768e66), C(18859672 bafd1661),
	  C(d3815c5f595e513e), C(44 b3bdbdc0fe061f), C(f5f43b2a73ad2df5),
	  C(7 c0e6434c8d7553c), C(d3815c5f595e513e), C(44 b3bdbdc0fe061f),
	  C(f5f43b2a73ad2df5), C(7 c0e6434c8d7553c), C(8 c05859060821002),
	  C(73629 a0d275008ce), C(860 c012879e6f00f), C(d48735a120d2c37c) },
	{ C(4e2 a10f1c409dfa5), C(6e684591f 5 da86bd), C(ff8c9305d447cadb),
	  C(c43ae49df25b1c86), C(d4f42115cee1ac8), C(a0e6a714471b975c),
	  C(a40089dec5fe07b0), C(c43ae49df25b1c86), C(d4f42115cee1ac8),
	  C(a0e6a714471b975c), C(a40089dec5fe07b0), C(18 c3d8f967915e10),
	  C(739 c747dbe05adfb), C(4 b0397b596e16230), C(3 c57d7e1de9e58d1) },
	{ C(bdf3383d7216ee3c), C(eed3a37e4784d324), C(247 cff656d081ba0),
	  C(76059e4 cb25d4700), C(e0af815fe1fa70ed), C(5 a6ccb4f36c5b3df),
	  C(391 a274cd5f5182d), C(76059e4 cb25d4700), C(e0af815fe1fa70ed),
	  C(5 a6ccb4f36c5b3df), C(391 a274cd5f5182d), C(ff1579baa6a2b511),
	  C(c385fc5062e8a728), C(e940749739a37c78), C(a093523a5b5edee5) },
	{ C(a22e8f6681f0267d), C(61e79 bc120729914), C(86ec13 c84c1600d3),
	  C(1614811 d59dcab44), C(d1ddcca9a2675c33), C(f3c551d5fa617763),
	  C(5 c78d4181402e98c), C(1614811 d59dcab44), C(d1ddcca9a2675c33),
	  C(f3c551d5fa617763), C(5 c78d4181402e98c), C(b43b4a9caa6f5d4c),
	  C(f112829bca2df8f3), C(87e5 c85db80d06c3), C(8eb4 bac85453409) },
	{ C(6997121 cae0ce362), C(ba3594cbcc299a07), C(7e4 b71c7de25a5e4),
	  C(16 ad89e66db557ba), C(a43c401140ffc77d), C(3780 a8b3fd91e68),
	  C(48190678248 a06b5), C(16 ad89e66db557ba), C(a43c401140ffc77d),
	  C(3780 a8b3fd91e68), C(48190678248 a06b5), C(d10deb97b651ad42),
	  C(3 a69f3f29046a24f), C(f7179735f2c6dab4), C(ac82965ad3b67a02) },
	{ C(9 bfc2c3e050a3c27), C(dc434110e1059ff3), C(5426055 da178decd),
	  C(cb44d00207e16f99), C(9 d9e99afedc8107f), C(56907 c4fb7b3bc01),
	  C(bcff1472bb01f85a), C(cb44d00207e16f99), C(9 d9e99afedc8107f),
	  C(56907 c4fb7b3bc01), C(bcff1472bb01f85a), C(516f 800f 74 ad0985),
	  C(f93193ade9614da4), C(9f 4 a7845355b75b7), C(423 c17045824dea5) },
	{ C(a3f37e415aedf14), C(8 d21c92bfa0dc545), C(a2715ebb07deaf80),
	  C(98 ce1ff2b3f99f0f), C(162 acfd3b47c20bf), C(62 b9a25fd39dc6c0),
	  C(c165c3c95c878dfe), C(98 ce1ff2b3f99f0f), C(162 acfd3b47c20bf),
	  C(62 b9a25fd39dc6c0), C(c165c3c95c878dfe), C(2 b9a7e1f055bd27c),
	  C(e91c8099cafaa75d), C(37e38 d64ef0263b), C(a46e89f47a1a70d5) },
	{ C(cef3c748045e7618), C(41dd 44f aef4ca301), C(6 add718a88f383c6),
	  C(1197ec a317e70a93), C(61f 9497e6 cc4a33), C(22e7178 d1e57af73),
	  C(5df 95 da0ff1c6435), C(1197ec a317e70a93), C(61f 9497e6 cc4a33),
	  C(22e7178 d1e57af73), C(5df 95 da0ff1c6435), C(934327643705e56 c),
	  C(11eb0 eec553137c9), C(1e6 b9b57ac5283ec), C(6934785 db184b2e4) },
	{ C(fe2b841766a4d212), C(42 cf817e58fe998c), C(29f 7f 493 ba9cbe6c),
	  C(2 a9231d98b441827), C(fca55e769df78f6c), C(da87ea680eb14df4),
	  C(e0b77394b0fd2bcc), C(2 a9231d98b441827), C(fca55e769df78f6c),
	  C(da87ea680eb14df4), C(e0b77394b0fd2bcc), C(f36a2a3c73ab371a),
	  C(d52659d36d93b71), C(3 d3b7d2d2fafbb14), C(b4b7b317d9266711) },
	{ C(d6131e688593a181), C(5 b658b282688ccd3), C(b9f7c066beed1204),
	  C(e9dd79bad89f6b19), C(b420092bae6aaf41), C(515f 9 bbd06069d77),
	  C(80664957 a02cbc29), C(e9dd79bad89f6b19), C(b420092bae6aaf41),
	  C(515f 9 bbd06069d77), C(80664957 a02cbc29), C(f9dc7a744a56d9b3),
	  C(7eb2 bdcd6667f383), C(c5914296fbdaf9d1), C(af0d5a8fec374fc4) },
	{ C(91288884ebf cf145), C(3df fd892d36403af), C(7 c4789db82755080),
	  C(634 acbe037edec27), C(878 a97fab822d804), C(fcb042af908f0577),
	  C(4 cbafc318bb90a2e), C(634 acbe037edec27), C(878 a97fab822d804),
	  C(fcb042af908f0577), C(4 cbafc318bb90a2e), C(68 a96d589d5e5654),
	  C(a752cb250bca1bc0), C(8f 228f 406024 aa7e), C(fc5408cf22a080b5) },
	{ C(754 c7e98ae3495ea), C(2030124 a22512c19), C(ec241579c626c39d),
	  C(e682b5c87fa8e41b), C(6 cfa4baff26337ac), C(4 d66358112f09b2a),
	  C(58889 d3f50ffa99c), C(33f c6ffd1ffb8676), C(36 db7617b765f6e2),
	  C(8df 41 c03c514a9dc), C(6707 cc39a809bb74), C(3f 27 d7bb79e31984),
	  C(a39dc6ac6cb0b0a8), C(33f c6ffd1ffb8676), C(36 db7617b765f6e2) },
};

void Check(uint64 expected, uint64 actual)
{
	if (expected != actual) {
		fprintf(stderr, "ERROR: expected %llx, but got %llx", expected,
			actual);
		++errors;
	}
}

void Test(const uint64 *expected, int offset, int len)
{
	const uint128 u = CityHash128(data + offset, len);
	const uint128 v = CityHash128WithSeed(data + offset, len, kSeed128);

	Check(expected[0], CityHash64(data + offset, len));
	Check(expected[1], CityHash64WithSeed(data + offset, len, kSeed0));
	Check(expected[2],
	      CityHash64WithSeeds(data + offset, len, kSeed0, kSeed1));
	Check(expected[3], Uint128Low64(u));
	Check(expected[4], Uint128High64(u));
	Check(expected[5], Uint128Low64(v));
	Check(expected[6], Uint128High64(v));

#ifdef __SSE4_2__
	const uint128 y = CityHashCrc128(data + offset, len);
	const uint128 z = CityHashCrc128WithSeed(data + offset, len, kSeed128);
	uint64 crc256_results[4];

	CityHashCrc256(data + offset, len, crc256_results);
	Check(expected[7], Uint128Low64(y));
	Check(expected[8], Uint128High64(y));
	Check(expected[9], Uint128Low64(z));
	Check(expected[10], Uint128High64(z));

	int i;

	for (i = 0; i < 4; i++)
		Check(expected[11 + i], crc256_results[i]);
#endif
}

int main(int argc, char **argv)
{
	setup();
	int i;
	for (i = 0; i < kTestSize - 1; i++)
		Test(testdata[i], i * i, i);

	Test(testdata[i], 0, kDataSize);

	return errors > 0;
}
